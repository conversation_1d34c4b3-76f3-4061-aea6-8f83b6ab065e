"""
Django settings for rollingbanners project.

Generated by 'django-admin startproject' using Django 3.2.7.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""

import json
import mimetypes
import os
import shutil
import ssl
from pathlib import Path
from urllib.parse import urlparse

import environ
import sentry_sdk
import structlog
from corsheaders.defaults import default_headers
from django.utils.translation import gettext_lazy as _
from google.oauth2 import service_account
from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.redis import RedisIntegration

from rollingbanners.sentry_config import (
    before_send,
    traces_sampler,
)

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent
mimetypes.add_type("applications/vnd.ms-excel", ".xlsx", strict=True)

# env file root directory
ROOT_DIR = environ.Path(__file__) - 2
env = environ.Env()
# reading .env file
environ.Env.read_env(env_file=ROOT_DIR(".env"))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
# WARNING: Changing secret key will break recce dynamic links
SECRET_KEY = env("SECRET_KEY")
PUBLIC_HASH_ID_KEY = env("PUBLIC_HASH_ID_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env.bool("DEBUG")

ALLOWED_HOSTS = env.list("ALLOWED_HOSTS")
METABASE_USERNAME = env.str("METABASE_USERNAME", default="admin")
METABASE_PASSWORD = env.str("METABASE_PASSWORD", default="admin")
METABASE_HOST = env.str("METABASE_HOST", default="admin")
METABASE_SESSION_URL = f"{METABASE_HOST}/api/session"
METASBASE_REDIS_KEY = "metabase_session_id"
METABASE_SECRET_KEY = env.str("METABASE_SECRET_KEY", default="admin")


DATA_UPLOAD_MAX_MEMORY_SIZE = 419430400  # 400MB

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.gis",
    # 3rd party
    "rest_framework",
    "rest_framework.authtoken",
    "dj_rest_auth",
    "corsheaders",
    "django_filters",
    "rangefilter",
    "wkhtmltopdf",
    "django_jsonform",
    "easy_select2",
    "django_celery_beat",
    "dal",  # Django Autocomplete Light
    "dal_select2",  # Select2 integration for DAL
    # apps
    "controlroom.apps.ControlroomConfig",
    "core.apps.CoreConfig",
    "comment.apps.CommentConfig",
    "authentication.apps.AuthenticationConfig",
    "authorization.apps.AuthorizationConfig",
    "recce.apps.RecceConfig",
    "bitrix.apps.BitrixConfig",
    "project.apps.ProjectConfig",
    "client.apps.ClientConfig",
    "vendor.apps.VendorConfig",
    "design.apps.DesignConfig",
    "element.apps.ElementConfig",
    "boq.apps.BoqConfig",
    "order.apps.OrderConfig",
    "progressreport.apps.ProgressreportConfig",
    "operations.apps.OperationsConfig",
    "smtp_email.apps.EmailConfig",
    "events.apps.EventsConfig",
    "notifications.apps.NotificationsConfig",
    "commentv2.apps.Commentv2Config",
    "order.invoice.apps.InvoiceConfig",
    "vms.apps.VmsConfig",
    "ratecontract.apps.RatecontractConfig",
    "microcontext.apps.MicrocontextConfig",
    "proposal.apps.ProposalConfig",
    "public.apps.PublicConfig",
    "chart.apps.ChartConfig",
    "sheet.apps.SheetConfig",
    "project.share.apps.ShareConfig",
    "core.user.apps.UserConfig",
    "core.role.apps.RoleConfig",
    "file_editor.apps.FileEditorConfig",
    "snags.apps.SnagsConfig",
    "snags.remark.apps.RemarkConfig",
    "vendorv2.apps.Vendorv2Config",
    "task.apps.TaskConfig",
    "reccev2.apps.Reccev2Config",
    "approval_request.apps.ApprovalRequestConfig",
    "approval_request.approval_hierarchy.apps.ApprovalHierarchyConfig",
    "expense.apps.ExpenseConfig",
    "timezone_field",
    "payment_request.apps.PaymentRequestConfig",
    "crm.apps.CrmConfig",
    "inventory.apps.InventoryConfig",
    "dashboard.apps.DashboardConfig",
    "schedular.apps.SchedularConfig",
    "callbacks.apps.CallbacksConfig",
    "integrations.apps.IntegrationsConfig",
    "report.apps.ReportConfig",
    "report.subscription.apps.SubscriptionConfig",
    "report.download.apps.DownloadConfig",
    "core.organization.apps.OrganizationConfig",
    "project_schedule.apps.ProjectScheduleConfig",
    "work_progress_v2.apps.WorkProgressV2Config",
    "execution_tracker.apps.ExecutionTrackerConfig",
    "payment_entry.apps.PaymentEntryConfig",
]

MIDDLEWARE = [
    "core.middlewares.RequestTelemetryMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.locale.LocaleMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "core.middlewares.ValidateAndDecodeTokenMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django_structlog.middlewares.RequestMiddleware",
    "core.middlewares.RequestIdResponseHeaderMiddleware",
    "core.middlewares.PlatformVersionResponseHeaderMiddleware",
    "core.middlewares.OneSessionPerUserMiddleware",
    "core.middlewares.ResetToDefaultTimezoneMiddleware",
]

ROOT_URLCONF = "rollingbanners.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "rollingbanners.wsgi.application"

# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

DATABASES = {
    # Looks for DATABASE_URL in environment file, primary
    "default": env.db(engine="django.contrib.gis.db.backends.postgis"),
    # "slave1": env.db("DATABASE_SLAVE1_URL", engine="django.contrib.gis.db.backends.postgis"),
}

REDIS_URI = env.str("REDIS_URI")
REDIS_URI_CELERY = env.str("REDIS_URI_CELERY")


# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_L10N = True

USE_TZ = True

# File Upload Max Size
FILE_UPLOAD_MAX_MEMORY_SIZE = ********  # 10 MB

# Number of days before expiration when the banner should be displayed
SUBSCRIPTION_BANNER_THRESHOLD_DAYS = 15


SILENCED_SYSTEM_CHECKS = ["auth.E003", "auth.W004"]

# List of finder classes that know how to find static files in
# various locations.
STATICFILES_FINDERS = (
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
)

GOOGLE_SERVICE_ACCOUNT_KEY = json.loads(os.getenv("GOOGLE_SERVICE_ACCOUNT_KEY"), strict=False)
GS_CREDENTIALS = service_account.Credentials.from_service_account_info(GOOGLE_SERVICE_ACCOUNT_KEY)

# AWS S3

AWS_ACCESS_KEY_ID = env("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = env("AWS_SECRET_ACCESS_KEY")
AWS_STORAGE_BUCKET_NAME = env("AWS_STORAGE_BUCKET_NAME")
AWS_S3_REGION_NAME = env("AWS_S3_REGION_NAME")
AWS_DEFAULT_ACL = None
AWS_S3_OBJECT_PARAMETERS = {"ACL": "private"}
AWS_S3_CUSTOM_DOMAIN = env.str("AWS_S3_CUSTOM_DOMAIN")
OLD_IMAGE_KIT_DOMAIN = env.str("OLD_IMAGE_KIT_DOMAIN")
AWS_CDN_DOMAIN = env.str("AWS_CDN_DOMAIN")

IS_CLOUD_STORAGE_ENABLED = env.bool("IS_CLOUD_STORAGE_ENABLED")  # DEBUG flag can be used
STATIC_LOCATION = "static"
MEDIA_LOCATION = ""
PROJECT_ROOT = os.path.join(os.path.dirname(os.path.abspath(__file__)), os.pardir)


if IS_CLOUD_STORAGE_ENABLED:
    # gc static settings
    STATIC_URL = f"https://{AWS_S3_CUSTOM_DOMAIN}/{STATIC_LOCATION}/"
    STATICFILES_STORAGE = "rollingbanners.storage_backends.StaticFileStorage"
    # gc media settings
    DEFAULT_FILE_STORAGE = "rollingbanners.storage_backends.PublicMediaFileStorage"
    MEDIA_URL = f"https://{AWS_S3_CUSTOM_DOMAIN}/{MEDIA_LOCATION}/"
    DEFAULT_MEDIA_URL = f"https://{AWS_CDN_DOMAIN}/"
    # gc private media settings
    PRIVATE_FILE_STORAGE = "rollingbanners.storage_backends.PrivateMediaFileStorage"

else:
    # for local file system, media and static in .gitignore
    DEFAULT_FILE_STORAGE = "django.core.files.storage.FileSystemStorage"
    STATIC_URL = "/static/"
    STATIC_ROOT = os.path.join(BASE_DIR, "static")
    MEDIA_URL = "/media/"
    MEDIA_ROOT = os.path.join(BASE_DIR, "media")
    DEFAULT_MEDIA_URL = "/media/"

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

AUTH_USER_MODEL = "core.User"

REST_FRAMEWORK = {
    "EXCEPTION_HANDLER": "rollingbanners.exception_handler.rb_exception_handler",
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rollingbanners.authentication.BearerAuthentication",
    ],
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.LimitOffsetPagination",
    "PAGE_SIZE": 10,
    "DEFAULT_PARSER_CLASSES": [
        "rest_framework.parsers.JSONParser",
        "rest_framework.parsers.FormParser",
        "rest_framework.parsers.MultiPartParser",
    ],
    "DEFAULT_SCHEMA_CLASS": "rest_framework.schemas.coreapi.AutoSchema",
    "DEFAULT_RENDERER_CLASSES": (
        "rollingbanners.response_renderer.JSONResponseRenderer",
        "rest_framework.renderers.JSONRenderer",
        "rest_framework.renderers.BrowsableAPIRenderer",
    ),
    "DEFAULT_FILTER_BACKENDS": ["django_filters.rest_framework.DjangoFilterBackend"],
    "NON_FIELD_ERRORS_KEY": "root",
}

# DATABASE_ROUTERS = ['rollingbanners.db_router.PrimaryReplicaRouter']
# DATABASE_ROUTERS = ["multidb.PinningReplicaRouter"]
# REPLICA_DATABASES = ["slave1"]

# added custom session store to use default database while selecting session
SESSION_ENGINE = "rollingbanners.session_store"

# CORS
if DEBUG:
    CORS_ALLOW_ALL_ORIGINS = True
else:
    CORS_ALLOWED_ORIGINS = env.list("CORS_ALLOWED_ORIGINS")

CORS_ALLOW_CREDENTIALS = True

# scheme forward
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

# Celery
CELERY_BROKER_URL = REDIS_URI_CELERY
CELERY_RESULT_BACKEND = REDIS_URI_CELERY
CELERY_ACCEPT_CONTENT = ["application/json"]
CELERY_RESULT_SERIALIZER = "json"
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_EXPIRES = 900
if urlparse(CELERY_BROKER_URL).scheme == "rediss":
    CELERY_BROKER_USE_SSL = {"ssl_cert_reqs": ssl.CERT_REQUIRED}
    CELERY_REDIS_BACKEND_USE_SSL = {"ssl_cert_reqs": ssl.CERT_REQUIRED}

if DEBUG:
    INSTALLED_APPS += [
        "django_extensions",
        "coreapi",
        "drf_yasg",
    ]

# Languages used for translation
LANGUAGES = [
    ("en", _("English")),
    ("hi", _("Hindi")),
]
LOCALE_PATHS = (BASE_DIR / "locale",)  # Here


PASSWORD_RESET_TIMEOUT_FOR_ADMIN_DIRECT_LOGIN = 5 * 60  # 5 minutes
EMAIL_LOGIN_MAGIC_LINK_TTL = 15 * 60
# Firebase
FIREBASE_API_KEY = env("FIREBASE_API_KEY")
FIREBASE_API_URL = env("FIREBASE_API_URL")
DOMAIN_URI_PREFIX = env("DOMAIN_URI_PREFIX")
ANDROID_PACKAGE_NAME = env("ANDROID_PACKAGE_NAME")
IOS_BUNDLE_ID = env("IOS_BUNDLE_ID")

# Dashboard
DASHBOARD_URL = env("DASHBOARD_URL")
METABASE_URL = env("METABASE_URL")
LINK_URL = env("LINK_URL", default="https://link.rdash.io/")
AWS_ASSET_DOMAIN = env("AWS_ASSET_DOMAIN", default="assets.rdash.dev")

# Analytics
IS_ANALYTICS_WORKER_ENABLED = env.bool("IS_ANALYTICS_WORKER_ENABLED", default=False)
ANALYTICS_API_URL = env("ANALYTICS_API_URL")

NOTIFICATION_SERVICE = "notifications.domain.services.NotificationService"
TASK_COUNT_REPO = "task.data.count_repo.TaskCountRepo"

# Custom cache
CUSTOM_CACHE = "rollingbanners.custom_caches.RedisCache"
CUSTOM_CACHE_KEY = "rb_{}"

UOM_CACHE = "core.caches.UnitOfMeasurementCache"

COMMENT_HELPER_SERVICES = "commentv2.domain.services.helper_services.CommentHelperService"
PROJECT_ASSIGNEE_HELPER = "project.domain.helpers.ProjectRoleAssigneeHelper"
CONTEXT_PROVIDER_HELPER = "controller.context_value_provider.helpers.ContextProviderHelper"
ORDER_GENERATE_EXCEL_SERVICE = "order.services.get_elements_excel_attachment"
REQUEST_TO_TASK_CALLBACK_FACTORY = "task.interfaces.factories.RequestToTaskCallbackFactory"
COMMENT_TO_TASK_CALLBACK_FACTORY = "task.interfaces.factories.CommentToTaskCallbackFactory"
WORKER_TO_SCHEDULAR_SERVICE = "schedular.callbacks.WorkerToSchedularService"
JOB_REPO = "schedular.data.repositries.JobRepo"
PROJECT_TO_WORK_PROGRESS_FACTORY = "work_progress_v2.interface.external.project.ProjectToWorkProgressFactory"
PROJECT_TO_LEAD_FACTORY = "crm.interface.external.project.ProjectToLeadFactory"
ORDER_TO_WORK_PROGRESS_FACTORY = "work_progress_v2.interface.external.order.OrderToWorkProgressFactory"

# Event handler
EVENT_HANDLER = "events.handler.EventHandler"

# CRM
LEAD_TO_PROJECT_SERVICE_FACTORY = "crm.interface.factories.factories.LeadToProjectServiceFactory"
COMPANY_TO_CLIENT_SERVICE_FACTORY = "crm.interface.factories.factories.CompanyToClientServiceFactory"

# Password reset
PASSWORD_RESET_DASHBOARD_URL = env.str("PASSWORD_RESET_DASHBOARD_URL")
PASSWORD_RESET_FROM_EMAIL = env.str("PASSWORD_RESET_FROM_EMAIL")
PASSWORD_RESET_TIMEOUT = env.int("PASSWORD_RESET_TIMEOUT")

# Email backend
EMAIL_BACKEND = env.str("EMAIL_BACKEND")
EMAIL_HOST = env.str("EMAIL_HOST")
EMAIL_HOST_USER = env.str("EMAIL_HOST_USER")
SUPPORT_EMAIL = env.str("SUPPORT_EMAIL")
EMAIL_HOST_PASSWORD = env.str("EMAIL_HOST_PASSWORD")
EMAIL_PORT = env.int("EMAIL_PORT")
EMAIL_USE_TLS = env.bool("EMAIL_USE_TLS")
EMAIL_USE_SSL = env.bool("EMAIL_USE_SSL")
SES_EMAIL_BACKEND = env.str("SES_EMAIL_BACKEND")
SES_EMAIL_HOST = env.str("SES_EMAIL_HOST")
SES_EMAIL_HOST_USER = env.str("SES_EMAIL_HOST_USER")
SES_EMAIL_HOST_PASSWORD = env.str("SES_EMAIL_HOST_PASSWORD")
SES_EMAIL_PORT = env.int("SES_EMAIL_PORT")
SES_FROM_EMAIL = env.str("SES_FROM_EMAIL")
SES_EMAIL_USE_TLS = env.bool("EMAIL_USE_TLS")
SES_EMAIL_USE_SSL = env.bool("EMAIL_USE_SSL")

# WhatsApp
WATI_TOKEN = env.str("WATI_TOKEN")
WATI_URL = env.str("WATI_URL")

# Twilio OTP Service
TWILIO_ACCOUNT_SID = env.str("TWILIO_ACCOUNT_SID")
TWILIO_AUTH_TOKEN = env.str("TWILIO_AUTH_TOKEN")
TWILIO_VERIFY_SERVICE_SID = env.str("TWILIO_VERIFY_SERVICE_SID")
OTP_SERVICE = "rollingbanners.otp_service.TwilioOTPService"

# Mixpanel
MIXPANEL_PROJECT_KEY = env.str("MIXPANEL_PROJECT_KEY")
# PostHog
POSTHOG_PROJECT_KEY = env.str("POSTHOG_PROJECT_KEY", default="")
POSTHOG_HOST = env.str("POSTHOG_HOST", default="https://us.i.posthog.com")

FIREBASE_SERVICE_ACCOUNT_KEY = GOOGLE_SERVICE_ACCOUNT_KEY

# recce rating update api key
RECCE_RATING_UPDATE_KEY = env("RECCE_RATING_UPDATE_KEY")
N8N_TRIGGER_API_KEY = env("N8N_TRIGGER_API_KEY")
MASTER_TOKEN_HASH = env("MASTER_TOKEN_HASH")

# Integrations
INTEGRATIONS_FACEBOOK_LEADGEN_WEBHOOK_TOKEN = env("INTEGRATIONS_FACEBOOK_LEADGEN_WEBHOOK_TOKEN")
INTEGRATIONS_FACEBOOK_APP_ID = env("INTEGRATIONS_FACEBOOK_APP_ID")
INTEGRATIONS_FACEBOOK_APP_SECRET = env("INTEGRATIONS_FACEBOOK_APP_SECRET")
INTEGRATIONS_FACEBOOK_API_VERSION = env("INTEGRATIONS_FACEBOOK_API_VERSION")
INTEGRATIONS_FACEBOOK_GRAPH_API_ENDPOINT = env("INTEGRATIONS_FACEBOOK_GRAPH_API_ENDPOINT")
INTREGRATIONS_POC_USER_EMAILS = env("INTREGRATIONS_POC_USER_EMAILS")


# Custom users to whom whatsapp notification needs to be sent
try:
    CUSTOM_USER_CONTACTS: list[dict] = env.json("CUSTOM_USER_CONTACTS", default=[])
    if CUSTOM_USER_CONTACTS and not isinstance(CUSTOM_USER_CONTACTS, list):
        raise Exception(
            "env parameter: CUSTOM_USER_CONTACTS if supplied it must be an instance of list with "
            "dict elements containing 'user' and 'contact' keys"
        )
except ValueError:
    CUSTOM_USER_CONTACTS = []

ALLOW_WATI_STAGE_MOVEMENT_ALERT = env.bool("ALLOW_WATI_STAGE_MOVEMENT_ALERT", default=True)

WKHTMLTOPDF_CMD = shutil.which("wkhtmltopdf")
WKHTMLTOPDF_CMD_OPTIONS = {
    "quiet": True,
}

SWAGGER_SETTINGS = {
    "SECURITY_DEFINITIONS": {"api_key": {"type": "apiKey", "in": "header", "name": "Authorization"}},
}


###### LOGGING #####

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "json_formatter": {
            "()": structlog.stdlib.ProcessorFormatter,
            "processor": structlog.processors.JSONRenderer(),
        },
        "plain_console": {
            "()": structlog.stdlib.ProcessorFormatter,
            "processor": structlog.dev.ConsoleRenderer(),
        },
        "key_value": {
            "()": structlog.stdlib.ProcessorFormatter,
            "processor": structlog.processors.KeyValueRenderer(key_order=["timestamp", "level", "event", "logger"]),
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "json_formatter",
        }
    },
    "loggers": {
        # "django_structlog": {
        #     "handlers": ["console"],
        #     "level": "INFO",
        # },
        # Make sure to replace the following logger's name for yours
        "root": {
            "handlers": ["console"],
            "level": "INFO",
        },
    },
}


structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        # add_module_and_lineno,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.CallsiteParameterAdder(
            {
                structlog.processors.CallsiteParameter.FILENAME,
                structlog.processors.CallsiteParameter.FUNC_NAME,
                structlog.processors.CallsiteParameter.LINENO,
            }
        ),
        structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
    ],
    context_class=structlog.threadlocal.wrap_dict(dict),
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

# Sentry Integration
SENTRY_DSN = env.str("SENTRY_DSN", default=None)
if SENTRY_DSN:
    sentry_sdk.init(
        dsn=SENTRY_DSN,
        integrations=[
            DjangoIntegration(
                transaction_style="url",
            ),
            RedisIntegration(),
        ],
        attach_stacktrace=True,
        max_breadcrumbs=10000,
        max_value_length=10000,
        traces_sample_rate=0.01,
        enable_tracing=True,
        send_default_pii=True,
        # transport=HttpTransportWithTimeOut,
        traces_sampler=traces_sampler,
        before_send=before_send,
    )
else:
    pass

VMS_TOKEN = env("VMS_TOKEN")
VMS_HOST = env("VMS_HOST")
FIREBASE_ASSET_LINKS = env.json("FIREBASE_ASSET_LINKS")
TEMPLATE_ENDPOINT = env("TEMPLATE_ENDPOINT", default="https://template-api.rdash.io/api")

# Analytics
IS_EVENT_ANALYTICS_ENABLED = env.bool("IS_EVENT_ANALYTICS_ENABLED", default=False)

INTERNAL_IPS = [
    "127.0.0.1",
    # Add the IP address of the machine that will be accessing the application.
]

CORS_ALLOW_HEADERS = [
    *default_headers,
    # ----old headers----
    "platform",
    "client-version",
    "device-os",
    "device-version",
    "bypass-user-id",
    "tz",
    # ----new headers----
    "rd-app-version",
    "rd-platform",
    "rd-session-id",
    "rd-client-request-id",
    "rd-device-model",
    "rd-device-version",
    "rd-sdk-version",
    "rd-lat",
    "rd-long",
    "rd-bypass-user-id",
    "rd-tz",
    "rd-work-progress-version",
]

CORS_EXPOSE_HEADERS = ["target-client-version", "rd-work-progress-version"]

IS_NON_PROD_ENV = env("IS_NON_PROD_ENV", default=False)

try:
    from .local_settings import *  # NOQA #pylint: disable=wildcard-import

    MIDDLEWARE += [
        "debug_toolbar.middleware.DebugToolbarMiddleware",
        # "pyinstrument.middleware.ProfilerMiddleware",
    ]
    INSTALLED_APPS += [
        "debug_toolbar",
    ]

    print("Using Local Settings")
    print(f"Using db:  {DATABASES}")
except Exception:
    pass

if env.bool("IS_DEMO_ENVIRONMENT", default=False):
    INSTALLED_APPS += [
        "clone.apps.CloneConfig",
    ]
    DATABASES["clone"] = env.db("DATABASE_URL_CLONE", engine="django.contrib.gis.db.backends.postgis")


LOGGING_REQUEST_LATENCY_BUCKETS = [
    ((0, 100), "0-100ms"),
    ((100, 200), "100-200ms"),
    ((200, 300), "200-300ms"),
    ((300, 400), "300-400ms"),
    ((400, 500), "400-500ms"),
    ((500, 700), "500-700ms"),
    ((700, 1000), "500-1000ms"),
    ((1000, float("inf")), "1000ms+"),
]

LOGGING_TELEMETRY_BLACKLIST_APIS = ["rollingbanners.views.healthz"]


TWILIO_ACCOUNT_MINIMUM_BALANCE_THRESHOLD = env.int("TWILIO_ACCOUNT_MINIMUM_BALANCE_THRESHOLD", default=21)
SLACK_SERVICE_ALERT_WEBHOOK_URL = env.str("SLACK_SERVICE_ALERT_WEBHOOK_URL", default="")
WATI_ACCOUNT_MINIMUM_CREDIT_THRESHOLD = env.int("WATI_ACCOUNT_MINIMUM_CREDIT_THRESHOLD", default=25)
WATI_TENANT_ID = env.str("WATI_TENANT_ID", None)
WATI_EMAIL = env.str("WATI_EMAIL", None)
