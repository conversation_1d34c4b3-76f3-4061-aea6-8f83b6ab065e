from django.contrib import admin
from django.urls import include, path, register_converter
from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from rest_framework.documentation import include_docs_urls

from rollingbanners.external_urls import v1, v2
from rollingbanners.hash_id_converter import HashIdConverter, PublicHashIdConverter
from rollingbanners.views import healthz

register_converter(HashIdConverter, "hash_id")
register_converter(PublicHashIdConverter, "public_hash_id")

# Schema views

schema_view = get_schema_view(
    openapi.Info(title="RDash API", default_version="v1"),
    public=True,
    permission_classes=[],
)

external_schema_view_v1 = get_schema_view(
    openapi.Info(title="RDash External API", default_version="v1"),
    public=True,
    permission_classes=[],
    patterns=v1.url_patterns,
)

external_schema_view_v2 = get_schema_view(
    openapi.Info(title="RDash External API", default_version="v2"),
    public=True,
    permission_classes=[],
    patterns=v2.url_patterns,
)

urlpatterns = [
    path("healthz", healthz, name="healthz"),
    path("admin/", admin.site.urls),
    path("api/v1/auth/", include("authentication.interface.urls.v1"), name="auth"),
    path("api/v1/core/", include("core.urls"), name="core"),
    path("api/v2/core/", include("core.urls_v2"), name="core-v2"),
    path("api/v1/client/", include("client.interface.urls.v1"), name="client"),
    path("api/v2/client/onboard/", include("client.onboard.interface.urls.client"), name="vendor"),
    path("api/v1/vendor/", include("vendor.interface.urls.v1"), name="vendor"),
    path("api/v2/vendor/", include("vendor.interface.urls.v2"), name="vendor"),
    path("api/v1/vendor/onboard/", include("vendorv2.interface.urls.urls"), name="vendor"),
    path("api/v2/vendor/onboard/", include("vendorv2.interface.urls.vendor"), name="vendor"),
    path("api/v1/project/", include("project.interface.urls.internal.project.v1"), name="project"),
    path("api/v2/project/", include("project.interface.urls.internal.project.v2"), name="project"),
    path("api/v1/app/project/", include("project.interface.urls.internal.app_urls"), name="app-project-urls"),
    path("api/v1/design/", include("design.interface.urls.v1"), name="design"),
    path("api/v2/design/", include("design.interface.urls.v2"), name="design-v2"),
    path(
        "api/v1/project/<hash_id:project_id>/design/",
        include("design.interface.urls.project.v1"),
        name="project-design",
    ),
    path(
        "api/v2/project/<hash_id:project_id>/design/",
        include("design.interface.urls.project.v2"),
        name="project-design-v2",
    ),
    path("api/v1/element/", include("element.interface.urls.internal.v1"), name="element"),
    path(
        "api/v1/client/<hash_id:client_id>/element/",
        include("element.interface.urls.internal.client_urls"),
        name="client-element",
    ),
    path(
        "api/v1/client/<hash_id:client_id>/project/",
        include("project.interface.urls.internal.client_urls"),
        name="client-project",
    ),
    path("api/v1/docs/", include_docs_urls(title="91Squarefeet API")),
    path("api/v1/swagger/", schema_view.with_ui("swagger", cache_timeout=0), name="schema-swagger-ui"),
    path("api/v1/redoc/", schema_view.with_ui("redoc", cache_timeout=0), name="schema-redoc"),
    # TODO: change url to api/v1/client/<hash_id:client_id>/project/<hash_id:project_id>/boq/
    path("api/v1/client/<hash_id:client_id>/boq/", include("boq.interface.urls.client_urls"), name="boq-project"),
    path(
        "api/v1/project/<hash_id:project_id>/vendor-order/",
        include("order.interface.urls.project.v1"),
        name="order-project",
    ),
    path(
        "api/v2/project/<hash_id:project_id>/vendor-order/",
        include("order.interface.urls.project.v2"),
        name="order-project",
    ),
    path(
        "api/v3/project/<hash_id:project_id>/vendor-order/",
        include("order.interface.urls.project.v3"),
        name="order-project",
    ),
    path("api/v1/order/", include("order.interface.urls.order_urls"), name="order-project"),
    path(
        "api/v1/project/<hash_id:project_id>/comment-v2/",
        include("commentv2.interface.urls.project.v1"),
        name="project-comment",
    ),
    path("api/v1/comment-v2/", include("commentv2.interface.urls.v1"), name="comment-v2"),
    path("api/v2/comment/", include("commentv2.interface.urls.v2"), name="comment-v2-org"),
    path("api/v1/vendor-order/", include("order.interface.urls.vendor_order"), name="vendor-order"),
    path(
        "api/v1/project/<hash_id:project_id>/progress-report/",
        include("progressreport.project_urls"),
        name="project-progress-report",
    ),
    path(
        "api/v2/project/<hash_id:project_id>/progress-report/",
        include("progressreport.project_urls_v2"),
        name="project-progress-report-v2",
    ),
    path(
        "api/v3/project/<hash_id:project_id>/progress-report/",
        include("progressreport.project_urls_v3"),
        name="project-progress-report-v3",
    ),
    path(
        "api/v1/approval-hierarchy/resource/",
        include("approval_request.approval_hierarchy.interface.urls"),
        name="approval-hierarchy",
    ),
    path("api/v1/boq/", include("boq.interface.urls.v1"), name="boq-org-urls"),
    path("api/v2/project/<hash_id:boq_id>/boq/", include("boq.interface.urls.project.v2"), name="boq-project-urls"),
    path("api/v3/project/<hash_id:boq_id>/boq/", include("boq.interface.urls.project.v3"), name="boq-project-urls"),
    path(
        "api/v2/app/project/<hash_id:boq_id>/boq/",
        include("boq.interface.urls.project.app.v2"),
        name="boq-project-urls",
    ),
    path("api/v1/project/snags/", include("snags.interface.urls.v1"), name="snags-urls"),
    path("api/v1/snags/", include("snags.interface.urls.v1"), name="snags-urls-v2"),
    path("api/v1/progress-report/", include("progressreport.urls"), name="progress-report"),
    path("api/v1/organization/<hash_id:org_id>/", include("core.organization_urls"), name="organization"),
    path("api/v1/email/", include("smtp_email.interface.urls.v1"), name="email"),
    path("api/v1/notification/", include("notifications.interface.urls.v1"), name="notifications"),
    path("vms/", include("vms.urls"), name="vms"),
    path(
        "api/v1/project/<hash_id:project_id>/file-editor/",
        include("file_editor.interface.urls.project.v1"),
        name="notifications",
    ),
    path(
        "api/v1/organization/<hash_id:org_id>/rate-contract/",
        include("ratecontract.interface.urls.v1"),
        name="organization-rate-contract",
    ),
    path("__debug__/", include("debug_toolbar.urls")),
    path("api/v1/vendor/<str:vendor_code>/", include("order.interface.urls.vendor_urls"), name="vendor-review"),
    path(
        "api/v1/project/<hash_id:project_id>/proposal/",
        include("proposal.interface.urls.project.v1"),
        name="project-proposal",
    ),
    path(
        "api/v2/project/<hash_id:project_id>/proposal/",
        include("proposal.interface.urls.project.v2"),
        name="project-proposal",
    ),
    path(
        "api/v1/project/<hash_id:project_id>/snags/",
        include("snags.interface.urls.project.v1"),
        name="snags-project-urls",
    ),
    path("api/v1/public/", include("public.urls"), name="public"),
    path("api/v1/project/<hash_id:project_id>/chart/", include("chart.interface.urls.project.v1"), name="chart"),
    path("api/v2/project/<hash_id:project_id>/chart/", include("chart.interface.urls.project.v2"), name="chart"),
    path("api/v3/project/<hash_id:project_id>/chart/", include("chart.interface.urls.project.v3"), name="chart"),
    path("api/v1/sheet/", include("sheet.interface.urls.v1"), name="sheet"),
    path(
        "api/v1/project/<hash_id:project_id>/sheet/", include("sheet.interface.urls.project.v1"), name="project-sheets"
    ),
    path("api/v2/task/", include("task.interfaces.urls.v2"), name="task-v2"),
    path(".well-known/", include("core.asset_link_urls"), name="public"),
    path("controlroom/", include("controlroom.interface.urls")),
    # External
    path(
        "api/v2/ext/project/<str:job_id>/order/",
        include("order.interface.urls.external.v2"),
        name="order-external-urls",
    ),
    path("api/v2/project/<hash_id:project_id>/recce/", include("reccev2.interface.urls.project.v2"), name="reccev2"),
    path("api/v2/app/recce/", include("reccev2.interface.urls.app.v2"), name="reccev2"),
    path("api/v2/recce/", include("reccev2.interface.urls.v2"), name="reccev2"),
    path("api/v1/expense/", include("expense.interface.urls.v1"), name="expense"),
    path("api/v2/order/", include("order.interface.urls.order_urls"), name="order-actions"),
    path("api/v2/order-snapshot/", include("order.interface.urls.snapshot_comment_urls"), name="order-actions"),
    path(
        "api/v1/project/<hash_id:project_id>/expense/",
        include("expense.interface.urls.project.v1"),
        name="expense-project-urls",
    ),
    path("api/v1/approval-request/", include("approval_request.interface.urls"), name="approval_request"),
    path("api/v1/payment-request/", include("payment_request.interface.urls"), name="payment_request"),
    path("api/v2/payment-request/", include("payment_request.interface.urls"), name="payment_request_v2"),
    path("api/v1/inventory/", include("inventory.interface.urls.v1"), name="inventory"),
    path(
        "api/v1/payment-request/payment-entry/",
        include("payment_request.interface.payment_entry_urls"),
        name="payment-entry",
    ),
    path("api/v1/invoice/", include("order.invoice.interface.urls.internal.urls"), name="invoice"),
    path("api/v2/invoice/", include("order.invoice.interface.urls.internal.urls"), name="invoice"),
    path("api/v1/crm/board/", include("crm.interface.urls.v1"), name="crm"),
    path(
        "api/v1/crm/board/<hash_id:board_id>/lead/<hash_id:lead_id>/quotation/",
        include("crm.interface.urls.quotation_urls"),
        name="crm-quotations",
    ),
    path("api/v1/app/crm/board/", include("crm.interface.urls.app_urls"), name="crm"),
    path("api/v1/dashboard/", include("dashboard.interface.urls.v1"), name="dashboard"),
    path("api/v2/callback/", include("callbacks.interfaces.urls.v2"), name="callbacks"),
    path("api/v1/integrations/", include("integrations.urls"), name="integrations"),
    path("api/v1/report/", include("report.urls"), name="report"),
    path("api/v1/events/", include("events.interface.urls.v1"), name="events"),
    path(
        "api/v1/project/<hash_id:project_id>/schedule/",
        include("project_schedule.interface.project_urls"),
        name="schedule-project-urls",
    ),
    path(
        "api/v1/project/<hash_id:project_id>/work-progress/",
        include("work_progress_v2.interface.urls.project.v1"),
        name="work-progress-project-urls-v1",
    ),
    path(
        "api/v1/work-progress/",
        include("work_progress_v2.interface.urls.v1"),
        name="work-progress-org-urls-v1",
    ),
    path(
        "api/v1/ext/swagger",
        external_schema_view_v1.with_ui("swagger", cache_timeout=0),
        name="external-schema-view-v1",
    ),
    *v1.url_patterns,
    # External V2 URLs
    path(
        "v2/swagger",
        external_schema_view_v2.with_ui("swagger", cache_timeout=0),
        name="external-schema-view-v2",
    ),
    *v2.url_patterns,
]
