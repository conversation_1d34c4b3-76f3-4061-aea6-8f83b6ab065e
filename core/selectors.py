from decimal import Decimal
from typing import List, Optional, Union

import structlog
from django.contrib.postgres.aggregates import ArrayAgg
from django.db.models import F, OuterRef, Prefetch, Q, QuerySet, Subquery
from django.db.models.functions import <PERSON><PERSON><PERSON>Object

from authorization.domain.constants import Actions
from client.data.selectors import fetch_all_client_mappings_exclude_self_mapping
from common.choices import PermissionScope, ReservedRoleNames, RoleType
from core.choices import OrganizationDocumentChoices
from core.constants import TIMEZONE_MAPPING
from core.entities import (
    CountryData,
    CurrencyData,
    OrganizationCountryConfigCacheData,
    TaxTypeData,
    TDSTypeData,
    TimezoneData,
)
from core.models import (
    Country,
    CountryCurrencyMapping,
    CountryTaxMapping,
    CountryTimezoneMapping,
    Currency,
    FromToOrgMapping,
    ItemExpenseType,
    ItemExpenseTypeCategory,
    Organization,
    OrganizationAddress,
    OrganizationConfig,
    OrganizationConfigRole,
    OrganizationDocument,
    OrganizationGSTNumber,
    OrganizationOrderPaymentTerm,
    OrganizationUser,
    PlatformVersion,
    PmcClientMapping,
    PmcVendorMapping,
    ProductionDrawingTag,
    Region,
    Role,
    RoleAction,
    RolePermission,
    TaxSlab,
    TaxType,
    Timezone,
    UnitOfMeasurement,
    UnitOfMeasurementOrgMapping,
    User,
    UserLoginHistory,
)

# from crm.data.models import BoardUser
from core.tnc_config.data.choices import OrganizationTnCTypeChoice
from rollingbanners.authentication import TokenData

logger = structlog.get_logger(__name__)


def organization_get(org_id: int) -> Optional[Organization]:
    return Organization.objects.select_related("country").filter(pk=org_id).first()


def role_permission_list(*, role_id: int) -> list[str]:
    return RolePermission.objects.filter(role_id=role_id).values_list("permission", flat=True).order_by("permission")


def organization_user_with_system_user_fetch_all(*, org_id: int) -> QuerySet:
    return (
        OrganizationUser.objects.select_related("user", "user__org", "user__default_project_role")
        .filter(organization_id=org_id, user__app_token__isnull=True)
        .all()
    )


def organization_user_fetch_all(*, org_id: int) -> Union[QuerySet, List[OrganizationUser]]:
    return organization_user_with_system_user_fetch_all(org_id=org_id).exclude(user_id=F("user__org__system_user_id"))


def organization_user_fetch_all_cache(*, org_id: int) -> Union[QuerySet, List[OrganizationUser]]:
    return OrganizationUser.objects.filter(organization_id=org_id).all()


def organization_admin_fetch_all(*, org_id: int) -> QuerySet:
    return OrganizationUser.objects.filter(organization_id=org_id, is_admin=True).all()


def organization_user_fetch_active(*, org_id: int) -> Union[QuerySet, List[OrganizationUser]]:
    return organization_user_fetch_all(org_id=org_id).filter(user__is_active=True)


def role_action_list(*, role_id: int) -> list[Actions]:
    return RoleAction.objects.filter(role_id=role_id).values_list("action", flat=True).order_by("action")


def role_fetch_all(*, org_id: int, scope: str) -> QuerySet:
    return Role.objects.filter(organization_id=org_id, scope=scope, is_active=True)


def project_user_role_fetch_all(*, org_id: int) -> QuerySet:
    return role_fetch_all(org_id=org_id, scope=PermissionScope.PROJECT)


def project_user_generic_role_fetch(*, org_id: int) -> QuerySet:
    return project_user_role_fetch_all(org_id=org_id).filter(
        ~Q(role_type=RoleType.DYNAMIC) | Q(name=ReservedRoleNames.PROJECT_POC.value)
    )


def organization_user_exists(*, org_id: int, user_id: int) -> bool:
    return organization_user_with_system_user_fetch_all(org_id=org_id).filter(user_id=user_id).exists()


def role_exists(*, scope: str, role_id: int) -> bool:
    return Role.objects.filter(id=role_id, scope=scope).exists()


def organization_user_fetch_active_using_user(*, user_id: int) -> QuerySet:
    return OrganizationUser.objects.select_related("role", "organization", "organization__config").filter(
        user_id=user_id, user__is_active=True
    )


def fetch_organization_config_using_org_id(org_id: int) -> Optional[OrganizationConfig]:
    return OrganizationConfig.objects.filter(organization_id=org_id).first()


def client_fetch_all(*, user: User) -> QuerySet:
    if not hasattr(user, "token_data") or not isinstance(user.token_data, TokenData):
        raise Exception("User object has no token_data")

    token_data: TokenData = user.token_data
    client_ids = (
        PmcClientMapping.objects.filter(pmc_id=token_data.org_id).values_list("client_id", flat=True)
        if token_data.org_id
        else list()
    )
    return Organization.objects.filter(id__in=client_ids)


def client_fetch_all_using_from_to_org_mapping(*, user: User) -> QuerySet:
    if not hasattr(user, "token_data") or not isinstance(user.token_data, TokenData):
        raise Exception("User object has no token_data")

    token_data: TokenData = user.token_data
    client_ids = (
        fetch_all_client_mappings_exclude_self_mapping(organization_id=token_data.org_id)
        .filter(is_client_active=True)
        .values_list("org_from_id", flat=True)
        if token_data.org_id
        else list()
    )
    return Organization.objects.filter(id__in=client_ids)


def vendor_fetch_all(*, user: User) -> QuerySet:
    if not hasattr(user, "token_data") or not isinstance(user.token_data, TokenData):
        raise Exception("User object has no token_data")

    token_data: TokenData = user.token_data
    vendor_ids = (
        PmcVendorMapping.objects.filter(pmc_id=token_data.org_id).values_list("vendor_id", flat=True)
        if token_data.org_id
        else list()
    )
    return Organization.objects.filter(id__in=vendor_ids)


def organization_user_role_fetch(
    *,
    org_id: int,
    user: User,
) -> QuerySet[Role]:
    roles = Role.objects.filter(organization_id=org_id, scope=PermissionScope.CORE)
    if hasattr(user, "token_data") and user.token_data.is_admin:
        return [Role(id=0, name="Admin")] + list(roles)
    return list(roles)


class OrganizationConfigRoleService:
    @staticmethod
    def organization_search_config_role_list(*, org_id: int):
        search_config_roles = (
            OrganizationConfigRole.objects.filter(organization_config_id=org_id, is_included=True)
            .values("role_id", "role__name", "is_visible")
            .annotate(id=F("role_id"), name=F("role__name"))
            .values("id", "name", "is_visible")
        )
        return search_config_roles

    @staticmethod
    def project_prefilled_role_list(*, org_id: int):
        return [
            org_conf_role.role
            for org_conf_role in OrganizationConfigRole.objects.filter(
                organization_config_id=org_id, is_required=True
            ).select_related("role")
        ]

    @staticmethod
    def project_list_assignment_role_ids(*, org_id: int):
        return OrganizationConfigRole.objects.filter(organization_config_id=org_id, is_included=True).values_list(
            "role_id"
        )

    @staticmethod
    def get_active_tnc_list(*, org_id: int, type: OrganizationTnCTypeChoice):
        return OrganizationOrderPaymentTerm.objects.filter(organization_id=org_id, type=type, is_active=True)

    @staticmethod
    def get_payment_term_description_list(*, org_id: int, payment_term_id: int):
        return OrganizationOrderPaymentTerm.objects.filter(organization_id=org_id, id=payment_term_id)


def get_user(email: str, phone_number: str):
    return User.objects.get(Q(email=email) | Q(phone_number=phone_number), deleted_at__isnull=True)


def get_user_by_email(email: str):
    return User.objects.filter(email=email).first()


def user_with_last_login_get(user_id: int):
    user: User = (
        User.objects.filter(id=user_id)
        .select_related("org")
        .annotate(
            user_last_login=Subquery(
                UserLoginHistory.objects.filter(user_id=OuterRef("id")).order_by("-id").values("login_at")[:1]
            )
        )
        .first()
    )
    return user


def organization_with_address_gst_pan_get(org_id: int):
    pan_subquery = OrganizationDocument.objects.filter(
        organization_id=OuterRef("id"), type=OrganizationDocumentChoices.PAN
    ).annotate(pan_data=JSONObject(id="id", file="file", file_name="name"))

    organization = (
        Organization.objects.select_related("client")
        .prefetch_related(
            Prefetch("addresses", queryset=OrganizationAddress.objects.all().order_by("id")),
            Prefetch("organization_gst", queryset=OrganizationGSTNumber.objects.all().order_by("created_at")),
        )
        .annotate(pan=Subquery(pan_subquery.values("pan_data")[:1]))
        .get(id=org_id)
    )
    return organization


def organization_active_admin_users_fetch(organization_id: int, exclude_poc_id: int):
    active_admin = (
        OrganizationUser.objects.select_related("user")
        .filter(
            organization_id=organization_id,
            user__deleted_at__isnull=True,
            user__email__isnull=False,
            user__is_active=True,
            is_admin=True,
        )
        .exclude(user_id=exclude_poc_id)
    )
    return active_admin


def organization_active_users_fetch(organization_id: int, exclude_poc_id: int):
    return (
        User.objects.filter(org_id=organization_id, deleted_at__isnull=True, is_active=True, email__isnull=False)
        .exclude(id=exclude_poc_id)
        .values_list("email", flat=True)
    )


def active_user_get_all():
    return User.objects.filter(is_active=True, deleted_at__isnull=True).all()


def active_user_get(user_id: int):
    return active_user_get_all().filter(id=user_id).first()


def active_users_get_using_ids(user_ids: List[int]):
    return active_user_get_all().filter(id__in=user_ids)


def platform_version_get(*, platform: str) -> QuerySet:
    return PlatformVersion.objects.filter(platform=platform).order_by("-id").first()


def get_org_regions(*, org_id: int) -> QuerySet:
    return Region.objects.filter(organization_id=org_id, is_active=True).all()


def organization_active_expense_item_type_fetch(*, org_id: int) -> QuerySet[ItemExpenseType]:
    return ItemExpenseType.objects.filter(organization_id=org_id, is_active=True)


def category_expense_item_type_fetch() -> QuerySet[ItemExpenseTypeCategory]:
    return ItemExpenseTypeCategory.objects.all()


def production_drawing_tags_active_fetch():
    return ProductionDrawingTag.objects.filter(is_active=True).all()


def gst_slab_active_fetch() -> List[Decimal]:
    return TaxSlab.objects.filter(is_active=True).order_by("tax_percent").values_list("tax_percent", flat=True)


def get_tax_slab_list(*, tax_id: Optional[int]) -> List[Decimal]:
    if tax_id is None:
        return gst_slab_active_fetch()
    return (
        TaxSlab.objects.filter(tax_type_id=tax_id, is_active=True)
        .order_by("tax_percent")
        .values_list("tax_percent", flat=True)
    )


def role_get(*, role_id: int, org_id: int) -> Role:
    return Role.objects.filter(id=role_id, organization_id=org_id).available().first()


def org_config_bcc_emails_get(org_id: int):
    config = OrganizationConfig.objects.filter(organization_id=org_id).first()
    if config and len(config.order_receiver_emails) > 0:
        return config.order_receiver_emails
    return []


def fetch_client_org_ids(org_id: int) -> List[int]:
    return list(FromToOrgMapping.objects.filter(org_to_id=org_id).values_list("org_from_id", flat=True))


def fetch_vendor_org_ids(org_id: int) -> List[int]:
    return list(FromToOrgMapping.objects.filter(org_from_id=org_id).values_list("org_to_id", flat=True))


def organization_details_fetch(organization_id: int):
    pan_subquery = OrganizationDocument.objects.filter(
        organization_id=OuterRef("id"), type=OrganizationDocumentChoices.PAN, deleted_at__isnull=True
    ).annotate(pan_data=JSONObject(id="id", number="organization__pan_number", file="file", file_name="name"))

    msme = OrganizationDocument.objects.filter(
        organization_id=OuterRef("id"), type=OrganizationDocumentChoices.MSME_DOC, deleted_at__isnull=True
    ).annotate(msme_data=JSONObject(id="id", number="organization__vendor__msme_id", file="file", file_name="name"))

    company_business_card = OrganizationDocument.objects.filter(
        organization_id=OuterRef("id"), type=OrganizationDocumentChoices.BUSINESS_CARD, deleted_at__isnull=True
    ).annotate(business_card_data=JSONObject(id="id", file="file", file_name="name"))

    aadhar_data_prefetch = Prefetch(
        "organization_document",
        queryset=OrganizationDocument.objects.filter(
            type=OrganizationDocumentChoices.AADHAR, deleted_at__isnull=True
        ).all(),
        to_attr="aadhar_data_prefetch",
    )
    return (
        Organization.objects.select_related("vendor")
        .annotate(
            pan=Subquery(pan_subquery.values("pan_data")[:1]),
            msme=Subquery(msme.values("msme_data")[:1]),
            company_business_card=Subquery(company_business_card.values("business_card_data")[:1]),
        )
        .prefetch_related(
            "organization_gst",
            "addresses",
            aadhar_data_prefetch,
        )
        .get(id=organization_id)
    )


def is_system_user(user_id: int, organization_id: int):
    return Organization.objects.filter(system_user_id=user_id, id=organization_id).exists()


def get_org_level_uoms_list(organization_id: int) -> list:
    uoms_from_org_mapping = (
        UnitOfMeasurementOrgMapping.objects.filter(organization_id=organization_id)
        .select_related("uom")
        .annotate(name=F("uom__name"))
        .values("uom_id", "name", "is_active")
        .annotate(id=F("uom_id"))
        .values("id", "name", "is_active")
        .all()
    )
    uoms_ids_from_org_mapping = uoms_from_org_mapping.values_list("id", flat=True)
    globally_enabled_uoms = UnitOfMeasurement.objects.filter(
        ~Q(id__in=uoms_ids_from_org_mapping), is_active=True
    ).values("id", "name", "is_active")
    org_level_uoms = list(uoms_from_org_mapping) + list(globally_enabled_uoms)
    return sorted(org_level_uoms, key=lambda x: x["id"])


def get_organization_active_uoms(organization_id: int):
    uom_org_mapping = UnitOfMeasurementOrgMapping.objects.aggregate(
        org_level_inactive=ArrayAgg(
            "uom_id",
            filter=Q(
                organization_id=organization_id,
                is_active=False,
            ),
        ),
        org_level_active=ArrayAgg(
            "uom_id",
            filter=Q(
                is_active=True,
                organization_id=organization_id,
            ),
        ),
    )
    uom_org_mapping_list = uom_org_mapping["org_level_inactive"] + uom_org_mapping["org_level_active"]
    globally_active_uoms_ids = UnitOfMeasurement.objects.filter(
        ~Q(id__in=uom_org_mapping_list), is_active=True
    ).values_list("id", flat=True)
    uom_ids = list(globally_active_uoms_ids) + list(uom_org_mapping["org_level_active"])

    return UnitOfMeasurement.objects.filter(id__in=uom_ids)


def get_tnc_list(org_id: int, type: OrganizationTnCTypeChoice) -> QuerySet:
    return OrganizationOrderPaymentTerm.objects.filter(organization_id=org_id, type=type).available().all()


def get_tnc(org_id: int, payment_term_id: int) -> OrganizationOrderPaymentTerm:
    return OrganizationOrderPaymentTerm.objects.filter(organization_id=org_id, id=payment_term_id).available().first()


def get_default_currency_tax_timezone():
    default_currency_data = CurrencyData(id=None, name="Indian Rupee", symbol="₹", code="INR", locale="en-IN")
    default_tax_data = TaxTypeData(id=None, name="GST", max_slab_percent=28)
    default_timezone_data = TimezoneData(id=None, name="Asia/Kolkata", locale="en-IN")
    return default_currency_data, default_tax_data, default_timezone_data


def get_organization_config(org_id: int) -> OrganizationCountryConfigCacheData:
    config: Optional[OrganizationConfig] = (
        OrganizationConfig.objects.filter(organization_id=org_id)
        .select_related("organization", "organization__country")
        .annotate(
            max_slab_percent=Subquery(
                CountryTaxMapping.objects.filter(
                    country_id=OuterRef("organization__country_id"), tax_type_id=OuterRef("tax_type_id")
                ).values("max_slab_percent")[:1]
            )
        )
        .first()
    )
    default_currency_data, default_tax_data, default_timezone_data = get_default_currency_tax_timezone()

    if config:
        if config.max_slab_percent is None:
            logger.error(
                f"Max slab percent is None for config: {config.pk}, org_id: {org_id}, orgs country: {config.organization.country_id}, tax_type: {config.tax_type_id}"  # noqa
            )
            raise ValueError("Max slab percent is None")
        return OrganizationCountryConfigCacheData(
            currency=(
                CurrencyData(
                    id=config.currency_id,
                    name=config.currency.name,
                    symbol=config.currency.symbol,
                    code=config.currency.code,
                    locale=config.currency.locale,
                )
                if config.currency
                else default_currency_data
            ),
            tax_type=(
                TaxTypeData(
                    id=config.tax_type_id,
                    name=config.tax_type.name,
                    max_slab_percent=config.max_slab_percent,
                )
                if config.tax_type
                else default_tax_data
            ),
            timezone=(
                TimezoneData(
                    id=config.timezone_id,
                    name=config.timezone.name,
                    locale=config.timezone.locale,
                )
                if config.timezone
                else default_timezone_data
            ),
            country=CountryData(
                id=config.organization.country_id,
                name=config.organization.country.name,
            ),
            tds_type=(
                TDSTypeData(
                    id=config.organization.country.tds_id,
                    name=config.organization.country.tds.name,
                    max_slab_percent=100,
                )
            )
            if config.organization.country.tds
            else None,
        )
    org = (
        Organization.objects.filter(id=org_id)
        .select_related("country")
        .prefetch_related(
            Prefetch(
                "country__country_timezone_mapping",
                CountryTimezoneMapping.objects.filter(is_default=True),
            ),
            Prefetch(
                "country__country_tax_mapping",
                CountryTaxMapping.objects.filter(is_default=True),
            ),
            Prefetch(
                "country__country_currency_mapping",
                CountryCurrencyMapping.objects.filter(is_default=True),
            ),
        )
        .first()
    )
    currency = org.country.country_currency_mapping.all().first().currency
    tax_type_mapping = org.country.country_tax_mapping.all().first()
    tax_type = tax_type_mapping.tax_type
    timezone = org.country.country_timezone_mapping.all().first().timezone
    return OrganizationCountryConfigCacheData(
        currency=(
            CurrencyData(
                id=currency.pk,
                name=currency.name,
                symbol=currency.symbol,
                code=currency.code,
                locale=currency.locale,
            )
            if currency
            else default_currency_data
        ),
        tax_type=(
            TaxTypeData(
                id=tax_type.pk,
                name=tax_type.name,
                max_slab_percent=tax_type_mapping.max_slab_percent,
            )
            if tax_type
            else default_tax_data
        ),
        timezone=(
            TimezoneData(
                id=timezone.pk,
                name=timezone.name,
                locale=timezone.locale,
            )
            if timezone
            else default_timezone_data
        ),
        country=CountryData(
            id=org.country_id,
            name=org.country.name,
        ),
    )


def fetch_default_org_timezone_obj():
    _, __, default_timezone_data = get_default_currency_tax_timezone()
    return default_timezone_data


def get_timezone_instance_by_name(name: str) -> Timezone:
    timezone_name = TIMEZONE_MAPPING.get(name) if name in TIMEZONE_MAPPING else name
    return Timezone.objects.filter(tz=timezone_name).first()


def get_country_currencies(country_id: int):
    return Currency.objects.filter(country_currency_mapping__country_id=country_id).all()


def get_country_tax_types(country_id: int):
    return TaxType.objects.filter(country_tax_mapping__country_id=country_id).all()


def get_country_timezones(country_id: int):
    return Timezone.objects.filter(country_timezone_mapping__country_id=country_id).all()


def get_country_with_timezone_tax_currency(country_id: int) -> Country:
    country = (
        Country.objects.filter(id=country_id)
        .prefetch_related(
            Prefetch(
                "country_timezone_mapping",
                CountryTimezoneMapping.objects.filter(is_default=True),
            ),
            Prefetch(
                "country_tax_mapping",
                CountryTaxMapping.objects.filter(is_default=True),
            ),
            Prefetch(
                "country_currency_mapping",
                CountryCurrencyMapping.objects.filter(is_default=True),
            ),
        )
        .first()
    )
    return country


def get_india_country_id():
    return Country.objects.filter(name__iexact="india").first().id
