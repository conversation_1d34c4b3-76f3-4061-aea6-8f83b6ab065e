import decimal
from functools import partial
from typing import Dict, List, Tuple

import structlog
from django.db.models import DecimalField, F, Q, Sum, Value
from django.db.models.functions import Coalesce
from django.db.transaction import on_commit

from boq.data.choices import BoqElementStatus
from boq.data.entities import BoqElementAction, BoqElementHistoryData
from boq.data.models import BoqElement
from boq.services.boq import update_boq_status
from boq.services.status_history import StatusService
from common.choices import OrderType
from common.element_base.entities import (
    GuidelineAttachmentData,
    GuidelineData,
    ProductionDrawingData,
)
from common.entities import ObjectStatus
from common.services import model_update
from core.models import User
from order.data.models import Vendor<PERSON>rder, VendorOrderElement
from order.data.selectors.selector_v1 import get_order_number
from order.domain.constants import OrderStatusEnum, OrderTypeEnum
from order.domain.entities.domain_entities import (
    OrderCreateData,
    OrderElementUpdateData,
    OrderSentData,
    OrderUpdateData,
    TermsAndConditionsAttachmentsData,
)
from order.services.order import (
    OrderBaseService,
    OrderUpdateService,
)
from order.services.trigger import order_sent_trigger
from proposal.data.entities import (
    OrderElementData,
    ProposalApproveData,
    ProposalApproveElementData,
)
from proposal.data.models import Proposal, ProposalDocument, ProposalElementMapping
from proposal.data.selectors import get_proposal, get_proposal_elements, get_work_order_from
from proposal.interface.exceptions import (
    ProposalApprovalDataValidationException,
    ProposalApprovedNotAllowed,
    ProposalElementApprovedNotAllowed,
)
from work_progress_v2.interface.factory_helper import WorkProgressTimelineSync

logger = structlog.getLogger(__name__)


def sort_list_by_id(*, elements: List):
    elements.sort(key=lambda x: x.id)


class ProposalToOrder:
    @classmethod
    def create_order_element_data(
        cls, proposal_elements: List[ProposalElementMapping], approved_elements: List[ProposalApproveElementData]
    ) -> List[OrderElementData]:
        element_objs = []
        logger.info("create_order_element_data started")
        for element, approved_element in zip(proposal_elements, approved_elements):
            if element.element.element_status == BoqElementStatus.CANCELLED.value:
                raise ProposalElementApprovedNotAllowed(
                    "Element is already cancelled, Please ask your vendor to make changes in proposal"
                )
            production_drawing_entities = []
            guideline_entities = []
            for production_drawing in element.production_drawings.all():
                production_drawing_entities.append(
                    ProductionDrawingData(
                        object_status=ObjectStatus.ADD,
                        name=production_drawing.name,
                        file=production_drawing.file,
                        tags=[tag.id for tag in production_drawing.tags.all()],
                    )
                )
            for guidelines in element.guidelines.all():
                guideline_entities.append(
                    GuidelineData(
                        object_status=ObjectStatus.ADD,
                        name=guidelines.name,
                        description=guidelines.description,
                        attachments=[
                            GuidelineAttachmentData(
                                object_status=ObjectStatus.ADD,
                                file=attachment.file,
                                name=attachment.name,
                                type=attachment.type,
                            )
                            for attachment in guidelines.attachments.all()
                        ],
                    )
                )
            if approved_element.object_status.value == ObjectStatus.DELETE.value:
                pass
            else:
                element_objs.append(
                    OrderElementData(
                        object_status=ObjectStatus.ADD,
                        name=element.name,
                        description=element.description,
                        uom=element.uom,
                        client_id=element.client_id,
                        serial_number=element.serial_number,
                        boq_element_id=element.element_id,
                        el_element_id=element.el_element_id,
                        boq_element_version=element.version,
                        custom_type=element.custom_type,
                        category_id=element.category_id,
                        item_type_id=element.item_type_id,
                        code=element.code,
                        preview_files=element.preview_files.all(),
                        guidelines=guideline_entities,
                        production_drawings=production_drawing_entities,
                        cancelled_element_id=None,
                        linked_element_id=element.element_id,
                        quantity=approved_element.quantity,
                        vendor_rate=approved_element.client_rate,
                        client_rate=0,
                        id=None,
                        discount_percent=element.discount_percent,
                        service_charge_percent=(
                            element.service_charge_percent
                            if element.is_service_charge_with_base_amount is not None
                            else 0
                        ),
                        is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
                        quantity_dimensions=approved_element.quantity_dimensions,
                        brand_name=element.brand_name,
                        tax_percent=element.tax_percent,
                        hsn_code=element.hsn_code,
                    )
                )
        logger.info("create_order_element_data finished")
        return element_objs

    @classmethod
    def create_tnc_attachments_data(
        cls, proposal_documents: List[ProposalDocument]
    ) -> List[TermsAndConditionsAttachmentsData]:
        return [
            TermsAndConditionsAttachmentsData(
                object_status=ObjectStatus.ADD, name=document.file_name, file=document.file_url, type=document.type
            )
            for document in proposal_documents
        ]

    @classmethod
    def create_order_data(
        cls, proposal_id: int, org_id: int, proposal_approve_data: ProposalApproveData
    ) -> Tuple[OrderCreateData, List[ProposalElementMapping]]:
        logger.info("create_order_data started")
        proposal: Proposal = (
            get_proposal(proposal_id=proposal_id)
            .select_related("proposal_from", "proposal_from__vendor")
            .prefetch_related("documents")
            .first()
        )
        sort_list_by_id(elements=proposal_approve_data.elements)
        proposal_elements = get_proposal_elements(proposal_id=proposal_id).select_related("element").order_by("id")
        logger.info("proposal element count", count=proposal_elements.count())
        elements = cls.create_order_element_data(
            proposal_elements=proposal_elements, approved_elements=proposal_approve_data.elements
        )

        return (
            OrderCreateData(
                shipping_address=proposal.shipping_address,
                org_from_id=proposal.proposal_for_id,
                org_to_id=proposal.proposal_from_id,
                origin_org_id=proposal.proposal_for_id,
                started_at=proposal.start_date,
                due_at=proposal.due_date,
                payment_tnc=proposal_approve_data.payment_tnc,
                other_tnc=proposal_approve_data.other_tnc,
                elements=elements,
                work_order_from=get_work_order_from(org_id=proposal.proposal_for_id),
                # terms_and_conditions_attachments=cls.create_tnc_attachments_data(
                #     proposal_documents=proposal_approve_data.terms_and_conditions_attachments
                # ),
                terms_and_conditions_attachments=proposal_approve_data.terms_and_conditions_attachments,
                deductions=[],
                purchase_orders=[],
                order_type=OrderType.OUTGOING,
                rate_contract_id=None,
                shipping_address_header=None,
                is_discounted=proposal.is_discounted,
                poc_id=None,
                is_service_charged=proposal.is_service_charged,
                type_of_order=OrderTypeEnum.REGULAR.value,
                to=proposal_approve_data.to,
                cc=proposal_approve_data.cc,
                bcc=proposal_approve_data.bcc,
                attachments=proposal_approve_data.attachments,
                subject=proposal_approve_data.subject,
                body=proposal_approve_data.body,
                document_config_data=proposal_approve_data.document_config_data,
            ),
            proposal_elements,
        )

    @classmethod
    def proposal_entity_segregation(
        cls,
        docs_list: List[ProposalApproveElementData],
    ) -> tuple[list[ProposalApproveElementData], list[ProposalApproveElementData], list[ProposalApproveElementData]]:
        to_create_docs = []
        to_update_docs = []
        to_delete_docs = []
        for doc in docs_list:
            if doc.quantity == 0 and doc.object_status == ObjectStatus.ADD:
                to_create_docs.append(doc)
            elif doc.quantity == 0 or doc.object_status == ObjectStatus.DELETE:
                to_delete_docs.append(doc)
            elif doc.object_status == ObjectStatus.ADD:
                to_create_docs.append(doc)
            elif doc.object_status == ObjectStatus.UPDATE:
                to_update_docs.append(doc)
        return (to_create_docs, to_update_docs, to_delete_docs)

    @classmethod
    def update_order_element_data(
        cls, element_entities: List[ProposalApproveElementData]
    ) -> Tuple[List[OrderElementUpdateData], List[BoqElementHistoryData]]:
        sort_list_by_id(elements=element_entities)
        to_create, to_update, _ = cls.proposal_entity_segregation(docs_list=element_entities)
        entity_id_dict = {entity.id: entity for entity in element_entities}
        proposal_elements: List[ProposalElementMapping] = (
            ProposalElementMapping.objects.filter(id__in=[entity.id for entity in element_entities])
            .select_related("element", "proposal", "proposal__project", "proposal__order")
            .all()
            .order_by("id")
        )
        if proposal_elements:
            if proposal_elements[0].proposal.order.outgoing_status == OrderStatusEnum.PENDING_APPROVAL.value:
                raise ProposalApprovedNotAllowed("Order Already in Pending Approval State")
        to_create_order_elements = []
        to_update_order_elements = []
        to_delete_order_elements = []
        for element in proposal_elements:
            if element.element.element_status == BoqElementStatus.CANCELLED.value:
                raise ProposalElementApprovedNotAllowed(
                    "Element is already cancelled, Please ask your vendor to make changes in proposal"
                )
            if entity_id_dict[element.pk].object_status == ObjectStatus.DELETE:
                to_delete_order_elements.append(element)
            elif entity_id_dict[element.pk].object_status == ObjectStatus.ADD:
                to_create_order_elements.append(element)
            elif entity_id_dict[element.pk].object_status == ObjectStatus.UPDATE:
                to_update_order_elements.append(element)
        if to_create_order_elements:
            source = f"{to_create_order_elements[0].proposal.project.job_id}                \
        /{to_create_order_elements[0].proposal.order.order_number}"
        elif to_update_order_elements:
            source = f"{to_update_order_elements[0].proposal.project.job_id}     \
        /{to_update_order_elements[0].proposal.order.order_number}"
        elif to_delete_order_elements:
            source = f"{to_delete_order_elements[0].proposal.project.job_id}      \
            /{to_delete_order_elements[0].proposal.order.order_number}"
        order_elements = []
        history_data = []
        for element, element_data in zip(to_create_order_elements, to_create):
            history_data.append(
                BoqElementHistoryData(
                    element_id=element.element_id,
                    action=BoqElementAction.ORDER_APPROVED,
                    item_type_id=element.element.item_type_id,
                    uom=element.element.uom,
                    quantity=element_data.quantity,
                    client_rate=element_data.client_rate,
                    budget_rate=element.element.budget_rate,
                    category_id=element.element.category_id,
                    status=StatusService.get_status(
                        action=BoqElementAction.ORDER_APPROVED, old_element_status=element.element.element_status
                    ),
                    source=source,
                    discount_percent=element.discount_percent,
                    service_charge_percent=element.service_charge_percent,
                    is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
                    quantity_dimensions=element_data.quantity_dimensions,
                    name=element.element.name,
                    description=element.element.description,
                    brand_name=element.brand_name,
                    tax_percent=element.tax_percent,
                    hsn_code=element.hsn_code,
                )
            )
            order_element_entity = OrderElementData(
                id=None,
                name=element.name,
                description=element.description,
                uom=int(element.uom),
                client_id=element.client_id,
                el_element_id=element.el_element_id,
                boq_element_id=element.element_id,
                custom_type=element.custom_type,
                cancelled_element_id=None,
                serial_number=element.serial_number,
                quantity=element_data.quantity,
                vendor_rate=element_data.client_rate,
                code=element.code,
                linked_element_id=element.element_id,
                category_id=element.category_id,
                item_type_id=element.item_type_id,
                client_rate=0,
                preview_files=[],
                guidelines=[],
                production_drawings=[],
                boq_element_version=element.version,
                discount_percent=element.discount_percent,
                object_status=ObjectStatus.ADD,
                service_charge_percent=element.service_charge_percent,
                is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
                quantity_dimensions=element_data.quantity_dimensions,
                brand_name=element.brand_name,
                tax_percent=element.tax_percent,
                hsn_code=element.hsn_code,
            )
            # setattr(order_element_entity, "object_status", ObjectStatus.ADD)
            order_elements.append(order_element_entity)
        for element, element_data in zip(to_update_order_elements, to_update):
            order_elements.append(
                OrderElementUpdateData(
                    id=element.element.order_element.id,
                    object_status=ObjectStatus.UPDATE,
                    name=element.name,
                    description=element.description,
                    uom=int(element.uom),
                    client_id=element.client_id,
                    el_element_id=element.el_element_id,
                    boq_element_id=element.element_id,
                    custom_type=element.custom_type,
                    cancelled_element_id=None,
                    serial_number=None,
                    quantity=element_data.quantity,
                    vendor_rate=element_data.client_rate,
                    code=element.code,
                    linked_element_id=element.element_id,
                    category_id=element.category_id,
                    item_type_id=element.item_type_id,
                    client_rate=0,
                    preview_files=[],
                    guidelines=[],
                    production_drawings=[],
                    discount_percent=element.discount_percent,
                    service_charge_percent=element.service_charge_percent,
                    is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
                    quantity_dimensions=element_data.quantity_dimensions,
                    brand_name=element.brand_name,
                    tax_percent=element.tax_percent,
                    hsn_code=element.hsn_code,
                )
            )
            # If client is making changes to those elements which has object_status null
            # (i.e changes not made by vendor or already approved element)
            old_element_status = element.element.element_status
            if old_element_status == BoqElementStatus.APPROVED.value:
                action = BoqElementAction.CHANGES_DONE
            else:
                action = BoqElementAction.CHANGE_APPROVED
            history_data.append(
                BoqElementHistoryData(
                    element_id=element.element_id,
                    action=action,
                    item_type_id=element.element.item_type_id,
                    uom=element.element.uom,
                    quantity=element_data.quantity,
                    client_rate=element_data.client_rate,
                    budget_rate=element.element.budget_rate,
                    category_id=element.element.category_id,
                    status=StatusService.get_status(action=action, old_element_status=old_element_status),
                    source=source,
                    discount_percent=element.discount_percent,
                    service_charge_percent=element.service_charge_percent,
                    is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
                    quantity_dimensions=element_data.quantity_dimensions,
                    name=element.element.name,
                    description=element.element.description,
                    brand_name=element.brand_name,
                    tax_percent=element.tax_percent,
                    hsn_code=element.hsn_code,
                )
            )

        for element in to_delete_order_elements:
            # If client is making changes to those elements which has object_status null
            # (i.e changes not made by vendor or already approved element)
            old_element_status = element.element.element_status
            if old_element_status in [BoqElementStatus.APPROVED.value, BoqElementStatus.CHANGE_REQUESTED.value]:
                action = BoqElementAction.CANCELLED
            else:
                action = BoqElementAction.CANCELLATION_APPROVED

            if old_element_status == BoqElementStatus.REQUESTED.value:
                action = BoqElementAction.REQUEST_REJECTED
                quantity = element.element.quantity
            else:
                quantity = 0
                order_elements.append(
                    OrderElementUpdateData(
                        id=element.element.order_element.id,
                        object_status=ObjectStatus.DELETE,
                        name=element.name,
                        description=element.description,
                        uom=int(element.uom),
                        client_id=element.client_id,
                        el_element_id=element.el_element_id,
                        boq_element_id=element.element_id,
                        custom_type=element.custom_type,
                        cancelled_element_id=None,
                        serial_number=None,
                        quantity=0,
                        vendor_rate=element.client_rate,
                        code=element.code,
                        linked_element_id=element.element_id,
                        category_id=element.category_id,
                        item_type_id=element.item_type_id,
                        client_rate=0,
                        preview_files=[],
                        guidelines=[],
                        production_drawings=[],
                        discount_percent=element.discount_percent,
                        service_charge_percent=element.service_charge_percent,
                        is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
                        quantity_dimensions=element.quantity_dimensions,
                        brand_name=element.brand_name,
                        tax_percent=element.tax_percent,
                        hsn_code=element.hsn_code,
                    )
                )
            history_data.append(
                BoqElementHistoryData(
                    element_id=element.element_id,
                    action=action,
                    item_type_id=element.element.item_type_id,
                    uom=element.element.uom,
                    quantity=quantity,
                    client_rate=element.client_rate,
                    budget_rate=element.element.budget_rate,
                    category_id=element.element.category_id,
                    status=StatusService.get_status(action=action, old_element_status=old_element_status),
                    source=source,
                    discount_percent=element.discount_percent,
                    service_charge_percent=element.service_charge_percent,
                    is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
                    quantity_dimensions=element.quantity_dimensions,
                    name=element.element.name,
                    description=element.element.description,
                    brand_name=element.brand_name,
                    tax_percent=element.tax_percent,
                    hsn_code=element.hsn_code,
                )
            )
        return order_elements, history_data

    @classmethod
    def create_order_update_data(
        cls, proposal: Proposal, proposal_approve_data: ProposalApproveData
    ) -> Tuple[OrderUpdateData, List[BoqElementHistoryData], decimal.Decimal]:
        order: VendorOrder = (
            VendorOrder.objects.annotate_elements_final_amount()
            .annotate(
                elements_amount_value=Coalesce(
                    F("elements_final_amount") + F("elements_tax_amount"), Value(0), output_field=DecimalField()
                ),
                # filter=Q(order_elements__deleted_at__isnull=True),
                deduction_amount_value=Sum(F("deductions__amount"), filter=Q(deductions__deleted_at__isnull=True)),
            )
            .filter(id=proposal.order_id)
            .first()
        )
        payment_tnc = proposal_approve_data.payment_tnc
        other_tnc = proposal_approve_data.other_tnc

        elements, history_data = cls.update_order_element_data(element_entities=proposal_approve_data.elements)
        order_entity = OrderUpdateData(
            shipping_address=proposal_approve_data.shipping_address,
            org_from_id=order.org_from_id,
            org_to_id=order.org_to_id,
            origin_org_id=proposal.proposal_for_id,
            started_at=proposal_approve_data.start_date,
            due_at=proposal_approve_data.due_date,
            payment_tnc=payment_tnc,
            other_tnc=other_tnc,
            elements=elements,
            work_order_from=order.work_order_from,
            terms_and_conditions_attachments=proposal_approve_data.terms_and_conditions_attachments,
            deductions=[],
            order_type=OrderType.OUTGOING,
            rate_contract_id=None,
            shipping_address_header=None,
            final_amount=proposal_approve_data.final_amount,
            is_discounted=proposal.is_discounted,
            is_service_charged=proposal.is_service_charged,
            poc_id=None,
            type_of_order=order.order_type,
            email_data={
                "to_receiver": proposal_approve_data.to,
                "cc_receiver": proposal_approve_data.cc,
                "bcc_receiver": proposal_approve_data.bcc,
                "subject": proposal_approve_data.subject,
                "body": proposal_approve_data.body,
                "attachments": proposal_approve_data.attachments,
            },
            document_config_data=proposal_approve_data.document_config_data,
        )
        setattr(order_entity, "id", order.id)
        final_value = order.deduction_amount_value if order.deduction_amount_value else decimal.Decimal(0)
        return (
            order_entity,
            history_data,
            order.elements_amount_value - final_value,
        )

    # TODO: Not Used Anymore
    # @classmethod
    # def create_email_data(cls, order_id: int, org_id: int, user: User, project_id: int) -> OrderSentData:
    #     from order.services import prepare_email_data

    #     email_data = prepare_email_data(
    #         vendor_order_id=order_id,
    #         project_id=project_id,
    #         user_email=user.email,
    #         org_id=org_id,
    #         fields=("item_name", "description", "item_code", "item_type", "Quantity", "UOM", "order_rate", "Amount"),
    #     )
    #     return OrderSentData(
    #         subject=email_data["subject"],
    #         body=None,
    #         to=email_data["recipient_emails"] if email_data["recipient_emails"] else [],
    #         cc=email_data["cc_emails"] if email_data["cc_emails"] else [],
    #         bcc=email_data["subject"] if email_data["bcc_emails"] else [],
    #         attachments=[email_data["attachment"]],
    #     )

    @classmethod
    def create_history_date_for_create_and_send_order(
        cls,
        order: VendorOrder,
        proposal_approve_data: ProposalApproveData,
        proposal_elements: List[ProposalElementMapping],
    ) -> List[BoqElementHistoryData]:
        element_id_data_dict = {element.id: element for element in proposal_approve_data.elements}
        source = f"{order.project.job_id}/{order.order_number}"
        history_data = []
        for element in proposal_elements:
            quantity = (
                element_id_data_dict[element.id].quantity if element.id in element_id_data_dict else element.quantity
            )
            client_rate = (
                element_id_data_dict[element.id].client_rate
                if element.id in element_id_data_dict
                else element.client_rate
            )
            quantity_dimensions = (
                element_id_data_dict[element.id].quantity_dimensions
                if element.id in element_id_data_dict
                else element.quantity_dimensions
            )
            object_status = element_id_data_dict[element.id].object_status.value
            if object_status == ObjectStatus.ADD.value:
                history_data.append(
                    BoqElementHistoryData(
                        element_id=element.element_id,
                        action=BoqElementAction.ORDER_APPROVED,
                        item_type_id=element.element.item_type_id,
                        uom=element.element.uom,
                        quantity=quantity,
                        client_rate=client_rate,
                        budget_rate=element.element.budget_rate,
                        category_id=element.element.category_id,
                        status=StatusService.get_status(
                            action=BoqElementAction.ORDER_APPROVED, old_element_status=element.element.element_status
                        ),
                        source=source,
                        discount_percent=element.discount_percent,
                        service_charge_percent=element.service_charge_percent,
                        is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
                        quantity_dimensions=quantity_dimensions,
                        name=element.element.name,
                        description=element.element.description,
                        brand_name=element.brand_name,
                        tax_percent=element.tax_percent,
                        hsn_code=element.hsn_code,
                    )
                )
            elif object_status == str(ObjectStatus.DELETE.value):
                history_data.append(
                    BoqElementHistoryData(
                        element_id=element.element_id,
                        action=BoqElementAction.REQUEST_REJECTED,
                        item_type_id=element.element.item_type_id,
                        uom=element.element.uom,
                        quantity=quantity,
                        client_rate=client_rate,
                        budget_rate=element.element.budget_rate,
                        category_id=element.element.category_id,
                        status=StatusService.get_status(
                            action=BoqElementAction.REQUEST_REJECTED, old_element_status=element.element.element_status
                        ),
                        source=source,
                        discount_percent=element.discount_percent,
                        service_charge_percent=element.service_charge_percent,
                        is_service_charge_with_base_amount=element.is_service_charge_with_base_amount,
                        quantity_dimensions=quantity_dimensions,
                        name=element.element.name,
                        description=element.element.description,
                        brand_name=element.brand_name,
                        tax_percent=element.tax_percent,
                        hsn_code=element.hsn_code,
                    )
                )
        return history_data

    @classmethod
    def create_and_send_order(
        cls, proposal: Proposal, org_id: int, user: User, project_id: int, proposal_approve_data: ProposalApproveData
    ) -> int:
        from order.services import OrderCreateProcessService, prepare_email_data
        from order.services.email import assign_role_and_permissions

        logger.info(
            "ProposalToOrder create_and_send_order started ",
            proposal_id=proposal.pk,
            org_id=org_id,
            user_id=user.pk,
            project_id=project_id,
        )

        order_data, proposal_elements = cls.create_order_data(
            proposal_id=proposal.pk, org_id=org_id, proposal_approve_data=proposal_approve_data
        )

        setattr(order_data, "proposal_id", proposal.pk)
        """
        This is to check if email data is present or not because in case of creation of 
        Proposal by System User, no email data is present
        """
        if not (hasattr(order_data, "to") and getattr(order_data, "to")):
            raise ProposalApprovalDataValidationException(
                "Please enter the email data properly. Fill all mandatory fields"
            )

        order, created_order_elements, _ = OrderCreateProcessService.process_create(
            order_entity=order_data,
            user=user,
            project_id=project_id,
            origin_org_id=org_id,
            order_type=order_data.order_type,
            is_sent=True,
            proposal_id=proposal.pk,
            direct_order=False,
            proposal_number=proposal.ref_number,
        )
        element_to_updated_fields_map = cls.sync_boq_elements(
            order_entity=order_data, user=user, proposal_id=proposal.pk
        )
        logger.info("boq elements synced")
        # Create link between newly added elements in order to boq
        cls.create_link_between_order_and_boq(order_entity=order_data, created_order_elements=created_order_elements)
        logger.info("link created between order and boq")
        action_histories = StatusService.create_bulk(
            history_data_list=cls.create_history_date_for_create_and_send_order(
                order=order, proposal_approve_data=proposal_approve_data, proposal_elements=proposal_elements
            ),
            user_id=user.pk,
        )
        logger.info("status service status created")

        if element_to_updated_fields_map:
            logger.info(
                "Update work progress element and create timeline and percentage history",
                source="proposal to order create",
            )
            WorkProgressTimelineSync.bulk_update_wp_element_and_create_timeline(
                element_to_updated_fields_map=element_to_updated_fields_map,
                action_histories=action_histories,
                user_id=user.pk,
                project_id=project_id,
                org_id=proposal.proposal_from_id,
            )
            logger.info("Work progress elements timeline and percentage history created.")

        email_data = prepare_email_data(
            vendor_order_id=order.pk,
            project_id=order.project_id,
            user_email=user.email,
            org_id=org_id,
            fields=("item_name", "description", "item_code", "item_type", "Quantity", "UOM", "order_rate", "Amount"),
            with_attachments=False,
        )
        logger.info("email data prepared")
        email_data["recipient_emails"] = (
            proposal_approve_data.to if proposal_approve_data.to else email_data["recipient_emails"]
        )
        email_data["cc_emails"] = proposal_approve_data.cc if proposal_approve_data.cc else email_data["cc_emails"]
        email_data["bcc_emails"] = proposal_approve_data.bcc if proposal_approve_data.bcc else email_data["bcc_emails"]
        vendor_id = order.actual_vendor_id
        assign_role_and_permissions(
            vendor_users_email=email_data["recipient_emails"],
            to_assign_org=vendor_id,
            project_id=project_id,
            assigned_by_org=order.origin_org_id,
            created_by_id=user.id,
        )

        logger.info("role and permissions assigned")
        order_number = get_order_number(vendor_order=order, project_id=project_id)
        on_commit(
            partial(
                order_sent_trigger,
                vendor_order_id=order.pk,
                org_to_id=vendor_id,
                org_from_id=proposal.proposal_for.pk,
                order_number=order_number,
                project_id=order.project_id,
            )
        )
        OrderBaseService.create_status_history(
            order_id=order.pk,
            incoming_status=order.incoming_status,
            outgoing_status=order.outgoing_status,
            user_id=user.pk,
        )
        logger.info("status history created")
        update_boq_status(boq_id=project_id, organization_id=order.org_to_id, user_id=user.pk)

        logger.info("vendor order aggregated data sent to vms")
        return order.pk, email_data

    @classmethod
    def _get_updated_final_fields(
        cls,
        boq_element: BoqElement,
        incoming_data: OrderElementUpdateData,
    ) -> list[str]:
        fields: list[str] = []
        if incoming_data.quantity != boq_element.proposal_element.quantity:
            fields.append("quantity")
        if incoming_data.item_type_id != boq_element.proposal_element.item_type_id:
            fields.append("item_type_id")
        if str(incoming_data.uom) != boq_element.proposal_element.uom:
            fields.append("uom")
        return fields

    @classmethod
    def sync_boq_elements(
        cls, order_entity: OrderUpdateData, user: User, proposal_id: int
    ) -> dict[BoqElement, list[str]]:
        to_update_boq_elements = []
        fields = [
            "client_rate",
            "quantity",
            "discount_percent",
            "service_charge_percent",
            "is_service_charge_with_base_amount",
            "quantity_dimensions",
            "updated_at",
            "updated_by_id",
            "tax_percent",
            "brand_name",
            "hsn_code",
        ]
        boq_element_ids = [element.linked_element_id for element in order_entity.elements]
        boq_elements = BoqElement.objects.select_related("work_progress_element", "proposal_element").filter(
            pk__in=boq_element_ids
        )

        element_id_to_updated_element_data_map = {}

        for element in order_entity.elements:
            element_id_to_updated_element_data_map[element.linked_element_id] = element

            element_id_to_updated_element_data_map[element.linked_element_id].client_rate = element.vendor_rate

        element_to_updated_fields_map: dict[BoqElement, list[str]] = {}
        for boq_element in boq_elements:
            instance, _, boq_updated_fields = model_update(
                instance=boq_element,
                data=element_id_to_updated_element_data_map[boq_element.id],
                fields=fields,
                updated_by_id=user.pk,
                save=False,
                clean=False,
            )
            to_update_boq_elements.append(instance)

            """
            BoqElement updated:
            - return boq_element and updated fields mapping
            - Not creating boq_element_history
            """
            updated_fields_for_wp_element = cls._get_updated_final_fields(
                boq_element=boq_element,
                incoming_data=element_id_to_updated_element_data_map[boq_element.id],
            )
            element_to_updated_fields_map[instance] = updated_fields_for_wp_element

        ProposalElementMapping.objects.filter(proposal_id=proposal_id).update(linked_element_id=None)
        BoqElement.objects.bulk_update(objs=to_update_boq_elements, fields=fields)
        return element_to_updated_fields_map

    @classmethod
    def create_link_between_order_and_boq(
        cls, order_entity: OrderUpdateData, created_order_elements: List[VendorOrderElement]
    ):
        to_create_link_elements = []
        order_element_entity_objects_status_add = [
            element for element in order_entity.elements if element.object_status == ObjectStatus.ADD
        ]
        for created_element, order_element_entity in zip(
            created_order_elements, order_element_entity_objects_status_add
        ):
            order_element_obj = VendorOrderElement()
            order_element_obj.linked_element_id = order_element_entity.linked_element_id
            order_element_obj.pk = created_element.pk
            to_create_link_elements.append(order_element_obj)
        VendorOrderElement.objects.bulk_update(objs=to_create_link_elements, fields=["linked_element_id"])

    @classmethod
    def update_order(cls, proposal: Proposal, proposal_approve_data: ProposalApproveData, user: User) -> int:
        from order.services import prepare_email_data

        order_entity, history_data, old_order_value = cls.create_order_update_data(
            proposal=proposal, proposal_approve_data=proposal_approve_data
        )
        order, _, created_order_elements = OrderUpdateService.process_update(
            order_entity=order_entity,
            user=user,
            project_id=proposal.project_id,
            is_sent=False,
            order_type=OrderType.OUTGOING,
            boq_sync_on=False,
        )
        # Creating Sync in Boq
        element_to_updated_fields_map = cls.sync_boq_elements(
            order_entity=order_entity, user=user, proposal_id=proposal.pk
        )
        # Create link between newly added elements in order to boq
        cls.create_link_between_order_and_boq(order_entity=order_entity, created_order_elements=created_order_elements)
        action_histories = StatusService.create_bulk(history_data_list=history_data, user_id=user.pk)

        if element_to_updated_fields_map:
            logger.info(
                "Update work progress element and create timeline and percentage history",
                source="proposal to order update",
            )
            WorkProgressTimelineSync.bulk_update_wp_element_and_create_timeline(
                element_to_updated_fields_map=element_to_updated_fields_map,
                action_histories=action_histories,
                user_id=user.pk,
                project_id=proposal.project_id,
                org_id=proposal.proposal_from_id,
            )
            logger.info("Work progress elements timeline and percentage history created.")

        email_data: Dict = prepare_email_data(
            vendor_order_id=order.id,
            project_id=order.project_id,
            with_attachments=False,
            user_email=user.email,
            org_id=user.token_data.org_id if user.token_data else None,
            is_order_updated=True,
        )
        update_boq_status(boq_id=proposal.project_id, organization_id=order.org_to_id, user_id=user.pk)
        # TODO: purchase add,delete and modify handle
        return order.pk, order_entity, email_data, old_order_value
