import uuid
from typing import List, Optional

import structlog
from django.db.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Count, Q, QuerySet, Value, When
from django.db.models.signals import post_save

from common.choices import OrganizationType, PermissionScope, RoleType
from core.models import Organization, Role, RolePermission
from core.role.repository import RolePermissionRepository as _RolePermissionRepository
from core.role.repository import RoleRepository as _RoleRepository
from project.data.models import ProjectOrganization

logger = structlog.get_logger(__file__)


class OrganizationRepository:
    def get_list(
        self,
        project_id: int,
        org_from_id: int,
        parent_org_ids: Optional[List[int]] = None,
        child_org_ids: Optional[List[int]] = None,
    ) -> QuerySet[Organization]:
        if not parent_org_ids:
            parent_org_ids = []
        if not child_org_ids:
            child_org_ids = []
        parent_org_ids.append(0)  # for testing only
        from order.data.models import VendorOrder

        return (
            Organization.objects.filter(id__in=parent_org_ids + child_org_ids)
            .annotate(
                total_sent_orders=Count(
                    "orders_to",
                    filter=Q(orders_to__project_id=project_id, orders_to__org_from=org_from_id)
                    & ~Q(orders_to__outgoing_status=VendorOrder.OutgoingStatus.NOT_SENT),
                    distinct=True,
                ),
                cancel_orders=Count(
                    "orders_to",
                    filter=Q(orders_to__project_id=project_id, orders_to__org_from=org_from_id)
                    & Q(orders_to__outgoing_status=VendorOrder.OutgoingStatus.CANCELLED),
                    distinct=True,
                ),
            )
            .annotate(
                is_parent=Case(
                    When(id__in=parent_org_ids, then=Value(True)), default=Value(False), output_field=BooleanField()
                )
            )
        )


class RoleRepository(_RoleRepository):
    def bulk_create(self, instance_list: List[Role]) -> List[Role]:
        logger.debug(f"instance_list: {instance_list}")
        return Role.objects.bulk_create(instance_list)

    def create_vendor_role(
        self, organization_id: int, created_by_id: int, updated_by_id: Optional[int] = None, save: bool = True
    ) -> Role:
        return self.create_role(
            name=str(uuid.uuid4()),
            organization_id=organization_id,
            created_by_id=created_by_id,
            type=OrganizationType.VENDOR,
            scope=PermissionScope.ORGANIZATION,
            role_type=RoleType.DYNAMIC,
            updated_by_id=updated_by_id,
            save=save,
        )

    def create_client_role(self, organization_id: int, created_by_id: int, save: bool = True) -> Role:
        return self.create_role(
            name=str(uuid.uuid4()),
            organization_id=organization_id,
            type=OrganizationType.CLIENT,
            scope=PermissionScope.ORGANIZATION,
            role_type=RoleType.DYNAMIC,
            created_by_id=created_by_id,
            save=save,
        )

    def create_many_vendor_role(
        self, organization_id: int, created_by_id: int, count: int, updated_by_id: Optional[int] = None
    ) -> List[Role]:
        roles = [
            self.create_vendor_role(
                organization_id=organization_id, created_by_id=created_by_id, updated_by_id=updated_by_id, save=False
            )
            for i in range(count)
        ]
        return self.bulk_create(instance_list=roles)

    def delete_using_ids(self, ids: List[int]):
        logger.debug(f"ids: {ids}")
        return Role.objects.filter(id__in=ids).delete()

    def update_last_modified_using_ids(self, ids: List[int], updated_by_id: int):
        logger.debug(f"ids: {ids}")
        return Role.objects.filter(id__in=ids).update(updated_by_id=updated_by_id)

    def change_role_type(self, id: int, type: OrganizationType):
        Role.objects.filter(id=id).update(type=type)


class ProjectOragnizationRepository:
    def bulk_create(self, instance_list: List[ProjectOrganization]) -> List[ProjectOrganization]:
        logger.debug(f"instance_list: {instance_list}")
        from project.domain.services import project_organization_status_create

        project_org_list = ProjectOrganization.objects.bulk_create(instance_list)
        for project_org in project_org_list:
            post_save.send(sender=ProjectOrganization, instance=project_org, created=True)
            project_organization_status_create(project_org_obj=project_org)
        return project_org_list

    def fetch_using_project_and_orgs(
        self, project_id: int, org_ids: List[int], assigned_by_id: int
    ) -> QuerySet[ProjectOrganization]:
        logger.debug(f"project_id: {project_id}, org_ids: {org_ids}, assigned_by_id: {assigned_by_id}")
        return ProjectOrganization.objects.filter(
            project_id=project_id, organization_id__in=org_ids, assigned_by_id=assigned_by_id
        ).select_related("role")

    def delete_using_ids(self, ids: List[int]):
        return ProjectOrganization.objects.filter(id__in=ids).delete()


class RolePermissionRepository(_RolePermissionRepository):
    def delete_using_roles(self, role_ids: List[int]):
        return RolePermission.objects.filter(role_id__in=role_ids).delete()

    def fetch_using_roles(self, role_ids: List[int]):
        return RolePermission.objects.filter(role_id__in=role_ids)
