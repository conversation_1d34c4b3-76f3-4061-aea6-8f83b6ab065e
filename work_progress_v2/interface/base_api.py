import structlog
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.status import HTTP_400_BAD_REQUEST

from authorization.domain.constants import Permissions
from common.constants import RequestHeaders
from core.apis import Org<PERSON><PERSON><PERSON><PERSON>
from project.interface.apis.internal.apis import <PERSON><PERSON><PERSON><PERSON><PERSON>
from work_progress_v2.domain.caches import WorkProgressDataVersionCache
from work_progress_v2.domain.entities import WorkProgressCacheKeyEntity
from work_progress_v2.interface.exceptions import WorkProgressVersionMismatch
from work_progress_v2.interface.permission import WorkProgressPermission

logger = structlog.get_logger(__name__)


class WorkProgressBaseApi(ProjectBaseApi):
    REQUIRED_PERMISSIONS = [Permissions.CAN_ACCESS_WORK_PROGRESS]
    permission_classes = [IsAuthenticated, WorkProgressPermission]

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)

        self.set_project_timezone()

    def validate_work_progress_version(self):
        incoming_version = self.get_work_progress_version()
        user_entity = self.get_project_user_entity()
        current_version = WorkProgressDataVersionCache.get(
            key=WorkProgressCacheKeyEntity(org_id=user_entity.org_id, project_id=user_entity.project_id)
        )

        if incoming_version != current_version:
            logger.info(
                "Work progress version mismatch",
                incoming_version=incoming_version,
                current_version=current_version,
            )
            raise WorkProgressVersionMismatch

    def get_version_mismatch_response(self):
        self.set_response_message("Work progress data has been updated by someone else. Please refresh and try again.")
        return Response({"is_refresh_required": True}, status=HTTP_400_BAD_REQUEST)

    def add_response_headers(self, response):
        response = super().add_response_headers(response)

        if RequestHeaders.WORK_PROGRESS_VERSION.value not in response.headers:
            user_entity = self.get_project_user_entity()
            response[RequestHeaders.WORK_PROGRESS_VERSION.value] = WorkProgressDataVersionCache.get(
                key=WorkProgressCacheKeyEntity(org_id=user_entity.org_id, project_id=user_entity.project_id)
            )
        return response


class WorkProgressOrgBaseApi(OrgBaseApi):
    REQUIRED_PERMISSIONS = []
