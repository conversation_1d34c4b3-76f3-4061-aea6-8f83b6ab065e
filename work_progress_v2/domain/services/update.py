import decimal

import structlog
from django.utils import timezone

from authorization.domain.constants import Permissions
from authorization.enums import PermissionLevelEnum
from boq.data.choices import BoqElementStatus
from common.exceptions import BaseValidationError, ValidationError
from common.utils import get_local_time
from core.entities import ProjectUserEntity
from core.exceptions import ResourceDoesNotExistException
from element.data.choices import ItemTypeUpdateMethodChoices
from project.interface.external.work_progress import WorkProgressToProjectService
from work_progress_v2.data.choices import WorkProgressElementActionChoices
from work_progress_v2.data.entities import (
    ElementAttachmentCreateDataEntity,
    ElementAttachmentDataEntity,
)
from work_progress_v2.data.models.element import WorkProgressElement
from work_progress_v2.data.models.milestone import ItemTypeMileStone
from work_progress_v2.domain.abstract_repos import WorkProgressAbstractRepo
from work_progress_v2.domain.caches import WorkProgressCache
from work_progress_v2.domain.entities import (
    ElementRemoveAttachmentEntity,
    UpdatedSectionDataEntity,
    UpdateElementDataEntity,
    UpdateElementOutputEntity,
    UpdateProgressElementDataEntity,
    UpdateProgressElementEntity,
    UpdateServiceBaseElementEntity,
    UpdateViaMilestoneEntity,
    UpdateViaProgressEntity,
    UpdateViaQuantityEntity,
)
from work_progress_v2.domain.enums import WorkProgressElementAttachmentPermissionActionEnum
from work_progress_v2.domain.services.day_wise import WorkProgressDayWiseService
from work_progress_v2.domain.services.locking import WorkProgressLockingService
from work_progress_v2.domain.services.permission import WorkProgressPermissionService
from work_progress_v2.domain.services.prepare import WorkProgressPrepareService
from work_progress_v2.domain.services.scope_data import WorkProgressScopeDataService
from work_progress_v2.domain.services.scope_timeline import WorkProgressScopeTimelineInteractor
from work_progress_v2.domain.services.status import WorkProgressStatusService
from work_progress_v2.interface.exceptions import WorkProgressVersionMismatch

logger = structlog.get_logger(__name__)


class WorkProgressUpdateService(WorkProgressPrepareService):
    class CancelledElementUpdateException(BaseValidationError):
        pass

    class IncomingProgressQuantityLessThanZeroException(BaseValidationError):
        pass

    class IncomingProgressPercentageLessThanZeroException(BaseValidationError):
        pass

    class MismatchUpdateMethodException(BaseValidationError):
        pass

    class IncomingProgressQuantityGreaterThanQuantityException(BaseValidationError):
        pass

    class IncomingProgressPercentageGreaterThanHundredException(BaseValidationError):
        pass

    class NoChangeInQuantityException(BaseValidationError):
        pass

    class NoChangeInPercentageException(BaseValidationError):
        pass

    class LockedException(BaseValidationError):
        pass

    class MilestoneNotFoundException(BaseValidationError):
        pass

    class WorkProgressInExecutionCompletedException(BaseValidationError):
        pass

    class MilestoneProgressPercentageLessThanZeroException(BaseValidationError):
        pass

    class MilestoneProgressPercentageGreaterThanHundredException(BaseValidationError):
        pass

    def __init__(
        self,
        repo: WorkProgressAbstractRepo,
        user_entity: ProjectUserEntity,
        element_id: int,
        locking_service: WorkProgressLockingService,
        status_service: WorkProgressStatusService,
        permission_service: WorkProgressPermissionService,
        cache: WorkProgressCache,
    ):
        self.repo = repo
        self.user_entity = user_entity
        self.element_id = element_id
        self.locking_service = locking_service
        self.status_service = status_service
        self.permission_service = permission_service
        self.current_time = get_local_time(timezone.now())
        self.cache = cache
        self.org_id = user_entity.org_id
        self.set_element()

    def get_element(self) -> UpdateServiceBaseElementEntity:
        element = self.repo.get_wp_element_entity(element_id=self.element_id)

        if not element.update_method:
            item_type_configs = self.cache.get_item_type_config()
            if element.item_type_id:
                element_item_type_config = item_type_configs.item_types.get(element.item_type_id)

                assert element_item_type_config is not None, "Item type config not found in cache"

                element.update_method = ItemTypeUpdateMethodChoices(element_item_type_config.default_update_method)
            else:
                element.update_method = ItemTypeUpdateMethodChoices(
                    item_type_configs.default_config.default_update_method
                )

        return UpdateServiceBaseElementEntity(
            id=element.id,
            quantity=element.quantity,
            progress_percentage=element.progress_percentage,
            input_progress_percentage=element.input_progress_percentage,
            input_progress_quantity=element.input_progress_quantity,
            previous_day_input_progress_percentage=element.previous_day_input_progress_percentage,
            previous_day_input_progress_quantity=element.previous_day_input_progress_quantity,
            previous_day_input_progress_milestone_id=element.previous_day_input_progress_milestone_id,
            unlocked_at=element.unlocked_at,
            progress_updated_at=element.progress_updated_at,
            last_day_progress_updated_at=element.last_day_progress_updated_at,
            update_method=element.update_method,
            element_status=element.element_status,
            uom_id=element.uom_id,
            item_type_id=element.item_type_id,
        )

    def set_element(self):
        self.element = self.get_element()

    def validate_element_status(self):
        if self.element.element_status == BoqElementStatus.CANCELLED:
            logger.info("Cancelled element update attempted", element_id=self.element_id)
            raise self.CancelledElementUpdateException("Cancelled element can not be updated")

    def validate_project_status(self):
        try:
            self.status_service.check_project_status_in_execution_completed()
        except WorkProgressStatusService.WorkProgressInExecutionCompletedException:
            raise self.WorkProgressInExecutionCompletedException(
                "Please Unmark execution completed in work progress section before this action"
            )

    def get_deletable_attachment_ids(self, attachments: list[ElementRemoveAttachmentEntity]) -> list[int]:
        deletable_attachment_ids = []
        now = self.current_time

        for attachment in attachments:
            if get_local_time(attachment.uploaded_at).date() == now.date():
                deletable_attachment_ids.append(attachment.id)

        return deletable_attachment_ids

    def update_via_quantity(self, incoming_progress_quantity: decimal.Decimal) -> UpdateViaQuantityEntity:
        self.validate_element_status()

        logger.info("Element data", element=self.element)

        if self.element.update_method != ItemTypeUpdateMethodChoices.QUANTITY:
            logger.info("Mismatch update method", update_method=self.element.update_method)
            raise self.MismatchUpdateMethodException("Update method is not quantity")

        if incoming_progress_quantity < 0:
            logger.info(
                "Incoming progress quantity less than zero",
                incoming_progress_quantity=incoming_progress_quantity,
            )
            raise self.IncomingProgressQuantityLessThanZeroException("Quantity can not be less than 0")

        if self.element.quantity < incoming_progress_quantity:
            logger.info(
                "Incoming progress quantity greater than quantity",
                incoming_progress_quantity=incoming_progress_quantity,
                element_quantity=self.element.quantity,
            )
            raise self.IncomingProgressQuantityGreaterThanQuantityException(
                "Progress quantity can not be greater than quantity"
            )

        if self.element.input_progress_quantity == incoming_progress_quantity:
            logger.info("No change in progress quantity", incoming_progress_quantity=incoming_progress_quantity)
            raise self.NoChangeInQuantityException("No change in progress quantity")

        self.validate_project_status()

        is_locked = self.locking_service.is_locked(
            unlocked_at=self.element.unlocked_at,
            progress_updated_at=self.element.progress_updated_at,
            last_day_progress_updated_at=self.element.last_day_progress_updated_at,
        )

        logger.info("Is locked", is_locked=is_locked)

        if is_locked and (
            self.element.previous_day_input_progress_quantity
            and incoming_progress_quantity < self.element.previous_day_input_progress_quantity
        ):
            raise self.LockedException("Progress cannot be less than the last update")

        progress_percentage = (
            (incoming_progress_quantity / self.element.quantity) * 100 if self.element.quantity else decimal.Decimal(0)
        )

        return UpdateViaQuantityEntity(quantity=incoming_progress_quantity, progress_percentage=progress_percentage)

    def update_via_percentage(self, incoming_progress_percentage: decimal.Decimal):
        self.validate_element_status()

        logger.info("Element data", element=self.element)

        if self.element.update_method != ItemTypeUpdateMethodChoices.PERCENTAGE:
            logger.info("Mismatch update method", update_method=self.element.update_method)
            raise self.MismatchUpdateMethodException("Update method is not percentage")

        if incoming_progress_percentage < 0:
            logger.info(
                "Incoming progress percentage less than zero",
                incoming_progress_percentage=incoming_progress_percentage,
            )
            raise self.IncomingProgressPercentageLessThanZeroException("Percentage can not be less than 0")

        if incoming_progress_percentage > 100:
            logger.info(
                "Incoming progress percentage greater than hundred",
                incoming_progress_percentage=incoming_progress_percentage,
            )
            raise self.IncomingProgressPercentageGreaterThanHundredException(
                "Progress percentage can not be greater than 100"
            )

        if self.element.input_progress_percentage == incoming_progress_percentage:
            logger.info(
                "No change in progress percentage",
                incoming_progress_percentage=incoming_progress_percentage,
            )
            raise self.NoChangeInPercentageException("No change in progress percentage")

        self.validate_project_status()

        is_locked = self.locking_service.is_locked(
            unlocked_at=self.element.unlocked_at,
            progress_updated_at=self.element.progress_updated_at,
            last_day_progress_updated_at=self.element.last_day_progress_updated_at,
        )

        logger.info("Is locked", is_locked=is_locked)

        if is_locked and (
            self.element.previous_day_input_progress_percentage
            and incoming_progress_percentage < self.element.previous_day_input_progress_percentage
        ):
            raise self.LockedException("Progress cannot be less than the last update")

        return UpdateViaProgressEntity(progress_percentage=incoming_progress_percentage)

    def update_via_milestone(self, milestone_id: int):
        self.validate_element_status()

        logger.info("Element data", element=self.element)

        if self.element.update_method != ItemTypeUpdateMethodChoices.MILESTONE:
            logger.info("Mismatch update method", update_method=self.element.update_method)
            raise self.MismatchUpdateMethodException("Update method is not milestone")

        try:
            converted_progress_percentage = self.repo.get_milestone_progress_percentage(milestone_id=milestone_id)
        except ItemTypeMileStone.DoesNotExist:
            logger.info("Milestone not found", milestone_id=milestone_id)
            raise self.MilestoneNotFoundException("Milestone not found")

        if converted_progress_percentage < 0:
            logger.info(
                "Milestone percentage less than zero",
                converted_progress_percentage=converted_progress_percentage,
            )
            raise self.IncomingProgressPercentageLessThanZeroException("Milestone percentage can not be less than 0")

        if converted_progress_percentage > 100:
            logger.info(
                "Milestone percentage greater than hundred",
                converted_progress_percentage=converted_progress_percentage,
            )
            raise self.IncomingProgressPercentageGreaterThanHundredException(
                "Milestone percentage can not be greater than 100"
            )

        if self.element.progress_percentage == converted_progress_percentage:
            logger.info("No change in milestone", converted_progress_percentage=converted_progress_percentage)
            raise self.NoChangeInPercentageException("No change in milestone")

        self.validate_project_status()

        is_locked = self.locking_service.is_locked(
            unlocked_at=self.element.unlocked_at,
            progress_updated_at=self.element.progress_updated_at,
            last_day_progress_updated_at=self.element.last_day_progress_updated_at,
        )

        logger.info("Is locked", is_locked=is_locked)

        if is_locked and (
            self.element.previous_day_input_progress_milestone_id
            and converted_progress_percentage < self.element.previous_day_input_progress_milestone_id
        ):
            raise self.LockedException("Progress cannot be less than the last update")

        return UpdateViaMilestoneEntity(progress_percentage=converted_progress_percentage, milestone_id=milestone_id)

    def add_attachments(
        self, attachments: list[ElementAttachmentCreateDataEntity]
    ) -> list[ElementAttachmentDataEntity]:
        logger.info("Adding attachments", element_id=self.element_id, attachments=attachments)

        if len(attachments) == 0:
            return []

        created_attachments = self.repo.create_attachments(element_id=self.element_id, attachments=attachments)

        logger.info("Attachments created", attachments=created_attachments)

        can_delete_attachments = self.permission_service.is_action_permitted(
            permission=Permissions.CAN_DELETE_FILE_IN_DPR,
            permission_type=PermissionLevelEnum.PROJECT,
        )

        logger.info("Can delete attachments", can_delete_attachments=can_delete_attachments)

        for attachment in created_attachments:
            if can_delete_attachments:
                attachment.actions.append(WorkProgressElementAttachmentPermissionActionEnum.CAN_DELETE)

        return created_attachments

    def delete_attachments(self, attachment_ids: list[int]):
        logger.info("Deleting attachments", element_id=self.element_id, attachment_ids=attachment_ids)
        if len(attachment_ids) == 0:
            return

        attachment_entities = self.repo.get_element_attachments_entity(
            element_id=self.element_id, attachment_ids=attachment_ids
        )

        logger.info("Fetched attachments", attachment_entities=attachment_entities)

        deletable_attachment_entities: list[ElementRemoveAttachmentEntity] = []

        for attachment in attachment_entities:
            deletable_attachment_entities.append(
                ElementRemoveAttachmentEntity(id=attachment.id, uploaded_at=attachment.uploaded_at)
            )

        deletable_attachment_ids = self.get_deletable_attachment_ids(attachments=deletable_attachment_entities)

        logger.info("Deletable attachments", deletable_attachment_ids=deletable_attachment_ids)

        if len(deletable_attachment_ids) != len(attachment_ids):
            raise self.LockedException("Attachments can not be deleted because of locking")

        self.repo.delete_attachments(element_id=self.element_id, attachment_ids=deletable_attachment_ids)

    def update_element(self, element_id: int, updated_data: UpdateElementDataEntity) -> UpdateElementOutputEntity:
        data, _ = self.repo.update_element(
            element_id=element_id,
            updated_data=updated_data,
        )

        item_type_configs = self.cache.get_item_type_config()

        return UpdateElementOutputEntity(
            updated_field=data.updated_field,
            updated_element_entity=self._prepare_element_list_detail(
                element=data.updated_element_entity,
                item_type_configs=item_type_configs,
            ),
            completion_amount_diff=data.completion_amount_diff,
            is_updated_today=data.is_updated_today,
            section_id=data.section_id,
            progress_percentage_diff=data.progress_percentage_diff,
            timeline_id=data.timeline_id,  # type: ignore[assignment]
        )


class WorkProgressUpdateInteractor:
    class Exception(BaseValidationError):
        pass

    class CancelledElementUpdateException(Exception):
        pass

    class NoChangeException(Exception):
        pass

    class LockedException(Exception):
        pass

    class MismatchUpdateMethodException(Exception):
        pass

    class IncomingProgressQuantityGreaterThanQuantityException(Exception):
        pass

    class IncomingProgressQuantityLessThanZeroException(Exception):
        pass

    class IncomingProgressPercentageLessThanZeroException(Exception):
        pass

    class IncomingProgressPercentageGreaterThanHundredException(Exception):
        pass

    class MilestoneProgressPercentageGreaterThanHundredException(Exception):
        pass

    class MilestoneProgressPercentageLessThanZeroException(Exception):
        pass

    class WorkProgressInExecutionCompletedException(Exception):
        pass

    class WorkProgressElementUpdateException(Exception):
        pass

    class MilestoneNotFoundException(Exception):
        pass

    class WorkProgressVersionMismatch(Exception):
        pass

    def __init__(
        self,
        service: WorkProgressUpdateService,
        is_vendor: bool,
        project_service: WorkProgressToProjectService,
        scope_data_service: WorkProgressScopeDataService,
        scope_timeline_interactor: WorkProgressScopeTimelineInteractor,
        day_wise_service: WorkProgressDayWiseService,
    ):
        self.service = service
        self.project_service = project_service
        self.is_vendor = is_vendor
        self.scope_data_service = scope_data_service
        self.scope_timeline_interactor = scope_timeline_interactor
        self.day_wise_service = day_wise_service

    def update_via_quantity(self, incoming_quantity: decimal.Decimal) -> UpdateProgressElementEntity:
        logger.info("Updating via quantity", incoming_quantity=incoming_quantity)

        try:
            update_entity = self.service.update_via_quantity(incoming_progress_quantity=incoming_quantity)
        except WorkProgressUpdateService.CancelledElementUpdateException as e:
            raise self.CancelledElementUpdateException(e)
        except WorkProgressUpdateService.NoChangeInQuantityException as e:
            raise self.NoChangeException(e)
        except WorkProgressUpdateService.LockedException as e:
            raise self.LockedException(e)
        except WorkProgressUpdateService.MismatchUpdateMethodException as e:
            raise self.MismatchUpdateMethodException(e)
        except WorkProgressUpdateService.IncomingProgressQuantityGreaterThanQuantityException as e:
            raise self.IncomingProgressQuantityGreaterThanQuantityException(e)
        except WorkProgressUpdateService.IncomingProgressQuantityLessThanZeroException as e:
            raise self.IncomingProgressQuantityLessThanZeroException(e)
        except WorkProgressUpdateService.WorkProgressInExecutionCompletedException as e:
            raise self.WorkProgressInExecutionCompletedException(e)

        try:
            updated_data = self.service.update_element(
                element_id=self.service.element_id,
                updated_data=UpdateElementDataEntity(
                    id=self.service.element_id,
                    progress_quantity_input=update_entity.quantity,
                    progress_percentage=update_entity.progress_percentage,
                    is_progress_manually_updated=True,
                    action=WorkProgressElementActionChoices.INPUT_QUANTITY_UPDATED,
                    current_visible_updated_method=ItemTypeUpdateMethodChoices.QUANTITY,
                ),
            )

            updated_scope_data = self.service.cache.get_updated_scope_data(
                scope_data_service=self.scope_data_service,
                completion_amount_diff=updated_data.completion_amount_diff,
                total_element_updated_today=1 if updated_data.is_updated_today else 0,
            )

            updated_section_data, updated_section_data_cache = self.service.cache.get_updated_section_data(
                scope_data_service=self.scope_data_service,
                section_data=[
                    UpdatedSectionDataEntity(
                        id=updated_data.section_id,
                        completion_amount_diff=updated_data.completion_amount_diff,
                        progress_percentage_diff=updated_data.progress_percentage_diff,
                    )
                ],
            )

            self.service.cache.validate_work_progress_version()
            new_version = self.service.cache.update_scope_and_section_cache(
                scope_data=updated_scope_data,
                section_data=updated_section_data_cache,
            )

            self.day_wise_service.update_project_daywise_data(data=updated_scope_data)
            self.day_wise_service.update_section_daywise_data(data=updated_section_data_cache)
            self.day_wise_service.update_element_daywise_data(data=[updated_data])
            self.service.status_service.update_work_report_status()
            self.service.status_service.update_project_status(is_vendor=self.is_vendor)
            self.project_service.update_progress_percentage(progress_percentage=updated_scope_data.total_progress)

            scope_timeline_data = self.scope_timeline_interactor.get_scope_timeline_data(scope_data=updated_scope_data)
            return UpdateProgressElementEntity(
                version=new_version,
                data=UpdateProgressElementDataEntity(
                    element=updated_data.updated_element_entity,
                    scope_data=updated_scope_data,
                    section_data=updated_section_data,
                    scope_timeline_data=scope_timeline_data,
                ),
            )

        except WorkProgressElement.DoesNotExist:
            raise ResourceDoesNotExistException()
        except WorkProgressVersionMismatch as e:
            raise self.WorkProgressVersionMismatch(str(e))
        except ValidationError as e:
            raise self.WorkProgressElementUpdateException(str(e))

    def update_via_percentage(self, incoming_percentage: decimal.Decimal) -> UpdateProgressElementEntity:
        try:
            update_entity = self.service.update_via_percentage(incoming_progress_percentage=incoming_percentage)
        except WorkProgressUpdateService.CancelledElementUpdateException as e:
            raise self.CancelledElementUpdateException(e)
        except WorkProgressUpdateService.NoChangeInPercentageException as e:
            raise self.NoChangeException(e)
        except WorkProgressUpdateService.LockedException as e:
            raise self.LockedException(e)
        except WorkProgressUpdateService.MismatchUpdateMethodException as e:
            raise self.MismatchUpdateMethodException(e)
        except WorkProgressUpdateService.IncomingProgressPercentageGreaterThanHundredException as e:
            raise self.IncomingProgressPercentageGreaterThanHundredException(e)
        except WorkProgressUpdateService.IncomingProgressPercentageLessThanZeroException as e:
            raise self.IncomingProgressPercentageLessThanZeroException(e)
        except WorkProgressUpdateService.WorkProgressInExecutionCompletedException as e:
            raise self.WorkProgressInExecutionCompletedException(e)

        try:
            updated_data = self.service.update_element(
                element_id=self.service.element_id,
                updated_data=UpdateElementDataEntity(
                    id=self.service.element_id,
                    progress_percentage=update_entity.progress_percentage,
                    progress_percentage_input=incoming_percentage,
                    is_progress_manually_updated=True,
                    action=WorkProgressElementActionChoices.INPUT_PERCENTAGE_UPDATED,
                    current_visible_updated_method=ItemTypeUpdateMethodChoices.PERCENTAGE,
                ),
            )

            updated_scope_data = self.service.cache.get_updated_scope_data(
                scope_data_service=self.scope_data_service,
                completion_amount_diff=updated_data.completion_amount_diff,
                total_element_updated_today=1 if updated_data.is_updated_today else 0,
            )

            updated_section_data, updated_section_data_cache = self.service.cache.get_updated_section_data(
                scope_data_service=self.scope_data_service,
                section_data=[
                    UpdatedSectionDataEntity(
                        id=updated_data.section_id,
                        completion_amount_diff=updated_data.completion_amount_diff,
                        progress_percentage_diff=updated_data.progress_percentage_diff,
                    )
                ],
            )

            self.service.cache.validate_work_progress_version()
            new_version = self.service.cache.update_scope_and_section_cache(
                scope_data=updated_scope_data,
                section_data=updated_section_data_cache,
            )

            self.day_wise_service.update_project_daywise_data(data=updated_scope_data)
            self.day_wise_service.update_section_daywise_data(data=updated_section_data_cache)
            self.day_wise_service.update_element_daywise_data(data=[updated_data])
            self.service.status_service.update_work_report_status()
            self.service.status_service.update_project_status(is_vendor=self.is_vendor)

            self.project_service.update_progress_percentage(progress_percentage=updated_scope_data.total_progress)

            scope_timeline_data = self.scope_timeline_interactor.get_scope_timeline_data(scope_data=updated_scope_data)

            return UpdateProgressElementEntity(
                version=new_version,
                data=UpdateProgressElementDataEntity(
                    element=updated_data.updated_element_entity,
                    scope_data=updated_scope_data,
                    section_data=updated_section_data,
                    scope_timeline_data=scope_timeline_data,
                ),
            )
        except WorkProgressVersionMismatch as e:
            raise self.WorkProgressVersionMismatch(str(e))
        except ValidationError as e:
            raise self.WorkProgressElementUpdateException(str(e))

    def update_via_milestone(self, milestone_id: int) -> UpdateProgressElementEntity:
        try:
            update_entity = self.service.update_via_milestone(milestone_id=milestone_id)
        except WorkProgressUpdateService.MilestoneNotFoundException as e:
            raise self.MilestoneNotFoundException(e)
        except WorkProgressUpdateService.CancelledElementUpdateException as e:
            raise self.CancelledElementUpdateException(e)
        except WorkProgressUpdateService.NoChangeInPercentageException as e:
            raise self.NoChangeException(e)
        except WorkProgressUpdateService.LockedException as e:
            raise self.LockedException(e)
        except WorkProgressUpdateService.MismatchUpdateMethodException as e:
            raise self.MismatchUpdateMethodException(e)
        except WorkProgressUpdateService.MilestoneProgressPercentageGreaterThanHundredException as e:
            raise self.MilestoneProgressPercentageGreaterThanHundredException(e)
        except WorkProgressUpdateService.MilestoneProgressPercentageLessThanZeroException as e:
            raise self.MilestoneProgressPercentageLessThanZeroException(e)
        except WorkProgressUpdateService.WorkProgressInExecutionCompletedException as e:
            raise self.WorkProgressInExecutionCompletedException(e)

        try:
            updated_data = self.service.update_element(
                element_id=self.service.element_id,
                updated_data=UpdateElementDataEntity(
                    id=self.service.element_id,
                    progress_percentage=update_entity.progress_percentage,
                    milestone_input_id=update_entity.milestone_id,
                    is_progress_manually_updated=True,
                    action=WorkProgressElementActionChoices.INPUT_MILESTONE_UPDATED,
                    current_visible_updated_method=ItemTypeUpdateMethodChoices.MILESTONE,
                ),
            )

            updated_scope_data = self.service.cache.get_updated_scope_data(
                scope_data_service=self.scope_data_service,
                completion_amount_diff=updated_data.completion_amount_diff,
                total_element_updated_today=1 if updated_data.is_updated_today else 0,
            )

            updated_section_data, updated_section_data_cache = self.service.cache.get_updated_section_data(
                scope_data_service=self.scope_data_service,
                section_data=[
                    UpdatedSectionDataEntity(
                        id=updated_data.section_id,
                        completion_amount_diff=updated_data.completion_amount_diff,
                        progress_percentage_diff=updated_data.progress_percentage_diff,
                    )
                ],
            )

            self.service.cache.validate_work_progress_version()
            new_version = self.service.cache.update_scope_and_section_cache(
                scope_data=updated_scope_data,
                section_data=updated_section_data_cache,
            )

            self.day_wise_service.update_project_daywise_data(data=updated_scope_data)
            self.day_wise_service.update_section_daywise_data(data=updated_section_data_cache)
            self.day_wise_service.update_element_daywise_data(data=[updated_data])
            self.service.status_service.update_work_report_status()
            self.service.status_service.update_project_status(is_vendor=self.is_vendor)

            self.project_service.update_progress_percentage(progress_percentage=updated_scope_data.total_progress)

            scope_timeline_data = self.scope_timeline_interactor.get_scope_timeline_data(scope_data=updated_scope_data)

            return UpdateProgressElementEntity(
                version=new_version,
                data=UpdateProgressElementDataEntity(
                    element=updated_data.updated_element_entity,
                    scope_data=updated_scope_data,
                    section_data=updated_section_data,
                    scope_timeline_data=scope_timeline_data,
                ),
            )
        except WorkProgressVersionMismatch as e:
            raise self.WorkProgressVersionMismatch(str(e))
        except ValidationError as e:
            raise self.WorkProgressElementUpdateException(str(e))

    def add_attachments(
        self, attachments: list[ElementAttachmentCreateDataEntity]
    ) -> list[ElementAttachmentDataEntity]:
        return self.service.add_attachments(attachments=attachments)

    def delete_attachments(self, attachment_ids: list[int]):
        self.service.delete_attachments(attachment_ids=attachment_ids)
