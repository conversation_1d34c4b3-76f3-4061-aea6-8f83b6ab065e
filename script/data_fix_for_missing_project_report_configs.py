from django.db import transaction
from django.db.models import Exists, OuterRef
from project.data.models import ProjectOrganization
from work_progress_v2.data.models import ProjectReportConfig
from project.domain.services import create_project_work_progress_configs


@transaction.atomic
def create_missing_work_progress_configs():
    missing_pos = ProjectOrganization.objects.annotate(
        has_config=Exists(
            ProjectReportConfig.objects.filter(
                project_id=OuterRef('project_id'),
                organization_id=OuterRef('organization_id')
            )
        )
    ).filter(has_config=False)

    for po in missing_pos:
        create_project_work_progress_configs(po)