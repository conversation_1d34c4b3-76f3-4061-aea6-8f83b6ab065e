name: Deploy RDash API ON AKS (stage)

on:
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  id-token: write
  contents: read

jobs:
  deploy:
    name: Setup, Build, Publish, and Deploy to Stage
    runs-on: ubuntu-latest
    environment:
      name: stage
      url: https://api.stage.rdash.dev/healthz
    timeout-minutes: 20

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Azure login
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.ORG_AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.ORG_AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.ORG_AZURE_SUBSCRIPTION_ID }}

      - uses: docker/login-action@v3
        with:
          registry: ${{ vars.ORG_STAGE_ACR_LOGIN_SERVER }}
          username: ${{ secrets.ORG_STAGE_ACR_USERNAME }}
          password: ${{ secrets.ORG_STAGE_ACR_PASSWORD }}

      - name: Sanitize Branch Name and Set Image Tag
        id: image
        run: |
          repo_name=$(echo "${{ github.repository }}" | cut -d'/' -f2)
          branch_name=$(echo "${GITHUB_REF##*/}" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9-]//g')
          git_hash=$(git rev-parse --short "$GITHUB_SHA")
          image_tag=${branch_name}.${git_hash}

          echo "tag=${image_tag}" >> $GITHUB_OUTPUT
          echo "name=${{ vars.ORG_STAGE_ACR_LOGIN_SERVER }}/${repo_name}:${image_tag}" >> $GITHUB_OUTPUT
          echo "cache=${{ vars.ORG_STAGE_ACR_LOGIN_SERVER }}/${repo_name}-cache" >> $GITHUB_OUTPUT

      - name: Make envfile
        uses: SpicyPizza/create-envfile@v2.0
        with:
          envkey_VMS_HOST: "https://vms-api.stage.rdash.dev"
          envkey_SES_EMAIL_BACKEND: ${{ vars.STAGE_SES_EMAIL_BACKEND }}
          envkey_SES_EMAIL_HOST: ${{ vars.STAGE_SES_EMAIL_HOST }}
          envkey_SES_EMAIL_HOST_USER: ${{ vars.STAGE_SES_EMAIL_HOST_USER }}
          envkey_SES_EMAIL_HOST_PASSWORD: ${{ vars.STAGE_SES_EMAIL_HOST_PASSWORD }}
          envkey_SES_EMAIL_PORT: ${{ vars.STAGE_SES_EMAIL_PORT }}
          envkey_SES_FROM_EMAIL: ${{ vars.STAGE_SES_FROM_EMAIL }}
          envkey_SES_EMAIL_USE_TLS: ${{ vars.STAGE_SES_EMAIL_USE_TLS }}
          envkey_SES_EMAIL_USE_SSL: ${{ vars.STAGE_SES_EMAIL_USE_SSL }}
          envkey_SECRET_KEY: ${{ secrets.STAGE_SECRET_KEY }}
          envkey_PUBLIC_HASH_ID_KEY: ${{ secrets.STAGE_PUBLIC_HASH_ID_KEY }}
          envkey_DATABASE_URL: ${{ secrets.STAGE_POSTGRES_URI }}
          envkey_DATABASE_SLAVE1_URL: ${{ secrets.STAGE_POSTGRES_URI }}
          envkey_REDIS_URI: ${{ vars.STAGE_AWS_REDIS_URI }}
          envkey_REDIS_URI_CELERY: ${{ vars.STAGE_REDIS_URI_CELERY }}
          envkey_FIREBASE_API_KEY: ${{ secrets.STAGE_FIREBASE_API_KEY }}
          envkey_EMAIL_HOST_PASSWORD: ${{ secrets.STAGE_EMAIL_HOST_PASSWORD }}
          envkey_WATI_TOKEN: ${{ secrets.STAGE_WATI_TOKEN }}
          envkey_RECCE_RATING_UPDATE_KEY: ${{ secrets.STAGE_RECCE_RATING_UPDATE_KEY }}
          envkey_GOOGLE_SERVICE_ACCOUNT_KEY: ${{ secrets.GOOGLE_SERVICE_ACCOUNT_KEY }}
          envkey_VMS_TOKEN: ${{ secrets.STAGE_VMS_TOKEN }}
          envkey_METABASE_PASSWORD: ${{ secrets.STAGE_METABASE_PASSWORD }}
          envkey_METABASE_SECRET_KEY: ${{ secrets.STAGE_METABASE_SECRET_KEY }}
          envkey_MIXPANEL_PROJECT_KEY: ${{ secrets.STAGE_MIXPANEL_PROJECT_KEY }}
          envkey_AWS_ACCESS_KEY_ID: ${{ secrets.STAGE_AWS_ACCESS_KEY_ID }}
          envkey_AWS_SECRET_ACCESS_KEY: ${{ secrets.STAGE_AWS_SECRET_ACCESS_KEY }}
          envkey_AWS_STORAGE_BUCKET_NAME: ${{ vars.STAGE_AWS_STORAGE_BUCKET_NAME }}
          envkey_AWS_S3_REGION_NAME: ${{ vars.STAGE_AWS_S3_REGION_NAME }}
          envkey_AWS_S3_CUSTOM_DOMAIN: ${{ vars.STAGE_AWS_S3_CUSTOM_DOMAIN }}
          envkey_AWS_CDN_DOMAIN: ${{ vars.STAGE_AWS_CDN_DOMAIN }}
          envkey_TWILIO_ACCOUNT_SID: ${{ vars.STAGE_TWILIO_ACCOUNT_SID}}
          envkey_TWILIO_AUTH_TOKEN: ${{ vars.STAGE_TWILIO_AUTH_TOKEN }}
          envkey_TWILIO_VERIFY_SERVICE_SID: ${{ vars.STAGE_TWILIO_VERIFY_SERVICE_SID }}
          envkey_MASTER_TOKEN_HASH: ${{ secrets.STAGE_MASTER_TOKEN_HASH }}
          envkey_N8N_TRIGGER_API_KEY: ${{ secrets.STAGE_N8N_TRIGGER_API_KEY }}
          envkey_INTREGRATIONS_POC_USER_EMAILS: ${{ vars.STAGE_INTREGRATIONS_POC_USER_EMAILS }}
          envkey_OLD_IMAGE_KIT_DOMAIN: ${{ vars.STAGE_OLD_IMAGE_KIT_DOMAIN }}

      - name: Build and Push Image
        uses: int128/kaniko-action@v1
        with:
          tags: ${{ steps.image.outputs.name }}
          file: Dockerfile
          cache-repository: ${{ steps.image.outputs.cache }}
          push: true
          cache: true
          verbosity: warn
          kaniko-args: |
            --snapshot-mode=redo

      - name: Update Image Tag in Kustomize
        run: cd kubernetes/stage && kustomize edit set image ghcr.io/rdash-tech/rdash-api=${{ steps.image.outputs.name }}

      - name: Setup Kubernetes and Deploy
        run: |
          az aks get-credentials --resource-group ${{ vars.ORG_AZURE_STAGE_RESOURCE_GROUP }} --name ${{ vars.ORG_AZURE_AKS_CLUSTER }} --overwrite-existing
          kubectl config set-context --current --namespace=${{ vars.STAGE_NAMESPACE }}
          kubectl create secret generic rdash-backend-env-secret --from-env-file .env --dry-run=client -o yaml | kubectl apply -f -
          kubectl apply -k kubernetes/stage

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: deployments
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_TITLE: Backend Stage Deployment
          SLACK_USERNAME: 91bot
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_MESSAGE: "${{ github.event.head_commit.message }}\n Api Url: https://api.stage.rdash.dev"
