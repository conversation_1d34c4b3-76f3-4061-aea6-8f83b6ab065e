from dal import autocomplete
from django.db.models import Q

from controlroom.data.models import (
    Organization,
    OrganizationUserRole,
)


class OrganizationAutocomplete(autocomplete.Select2QuerySetView):
    def get_queryset(self):
        qs = Organization.objects.all()

        if self.q:
            qs = qs.filter(Q(name__icontains=self.q) | Q(id__icontains=self.q))
        return qs


class OrganizationUserRoleAutoComplete(autocomplete.Select2QuerySetView):
    def get_queryset(self):
        qs = OrganizationUserRole.objects.all().select_related("organization")

        if self.q:
            qs = qs.filter(
                Q(name__icontains=self.q) | Q(id__icontains=self.q) | Q(organization__name__icontains=self.q)
            )
        return qs
