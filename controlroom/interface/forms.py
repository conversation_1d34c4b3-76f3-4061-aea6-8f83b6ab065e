import re
from typing import Any

from dal import autocomplete
from django import forms
from django.contrib.auth.forms import UserCreationForm as _UserCreationForm
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from easy_select2 import apply_select2

from authentication.data.models import AppToken
from client.data.models import Client as _Client
from common.choices import StateChoices, VendorStatusChoices
from common.regex import RegularExpression
from controlroom.data.models import (
    PMC,
    BoardStageProxy,
    City,
    CityStateMapping,
    ClientData,
    Country,
    CountryCurrencyMapping,
    CountryTaxMapping,
    CountryTimezoneMapping,
    CustomDashboardCollection,
    DesignFileTag,
    FromToOrgMapping,
    GstSlab,
    Organization,
    OrganizationAddress,
    OrganizationConfig,
    OrganizationConfigCorePermission,
    OrganizationConfigRole,
    OrganizationDocument,
    OrganizationGSTNumberData,
    OrganizationUserRole,
    ProductionDrawingTag,
    RolePermission,
    State,
    UnitOfMeasurement,
    User,
    Vendor,
    VendorData,
)
from core.caches import UnitOfMeasurementCache
from crm.data.models import Board
from element.data.models import ElementCategory, LibraryClientVendorMapping
from integrations.models import FacebookPage, FacebookPageCrmBoardMapping
from project.domain.helpers import ProjectOrganizationHelper
from vendor.data.models import Vendor as _Vendor
from vendorv2.domain.services import VendorServiceV2


class AppTokenAddForm(forms.ModelForm):
    organization = forms.ModelChoiceField(
        # queryset=Organization.objects.all(), required=True, widget=apply_select2(forms.Select)
        queryset=Organization.objects.all(),
        required=True,
        widget=autocomplete.ModelSelect2(
            url="organization-autocomplete",  # URL of the autocomplete view
        ),
    )

    def save_m2m(self) -> None:
        pass

    class Meta:
        model = AppToken
        fields = ["organization"]


class AppTokenChangeForm(forms.ModelForm):
    role = forms.ModelChoiceField(
        queryset=OrganizationUserRole.objects.all().select_related("organization"),
        required=True,
        # widget=apply_select2(forms.Select),
        widget=autocomplete.ModelSelect2(
            url="organization-user-role-autocomplete",  # URL of the autocomplete view
        ),
    )

    def save_m2m(self) -> None:
        pass

    class Meta:
        model = AppToken
        fields = ["role"]


class PmcClientMappingModelForm(forms.ModelForm):
    class Meta:
        widgets = {
            "client": apply_select2(forms.Select),
            "pmc": apply_select2(forms.Select),
        }

    def clean(self):
        cleaned_data = super().clean()
        if not _Client.objects.filter(pk=cleaned_data.get("client").pk).exists():
            raise forms.ValidationError("Invalid Client for PMC Mapping. Client data entry not found.")
        return cleaned_data


class LibrarySharedMappingsModelForm(forms.ModelForm):
    def clean(self):
        cleaned_data = super().clean()
        library = cleaned_data.get("library")
        shared_by = cleaned_data.get("client")
        mapping = LibraryClientVendorMapping.objects.filter(library_id=library.id, vendor_id=shared_by.id).exists()
        if not mapping and library.client_id != shared_by.id:
            raise forms.ValidationError(f"{shared_by} does not have access to this library.")
        return cleaned_data

    class Meta:
        widgets = {
            "client": apply_select2(forms.Select),
            "vendor": apply_select2(forms.Select),
            "library": apply_select2(forms.Select),
        }
        labels = {
            "client": "Shared by",
            "vendor": "Shared to",
        }


class ClientStoreTypeForm(forms.ModelForm):
    class Meta:
        widgets = {
            "client": apply_select2(forms.Select),
            "store_type": apply_select2(forms.Select),
        }


class OrganizationConfigEditModelForm(forms.ModelForm):
    class Meta:
        widgets = {
            "poc": apply_select2(forms.Select),
            "sales_poc": apply_select2(forms.Select),
            "tam_poc": apply_select2(forms.Select),
        }
        model = OrganizationConfig

        exclude = ("domain_name",)

    def __init__(self, *args, **kwargs):
        super(OrganizationConfigEditModelForm, self).__init__(*args, **kwargs)
        if "sales_poc" in self.fields:
            self.fields["sales_poc"].queryset = User.objects.filter(is_staff=True, email__icontains="@rdash.io")
        if "tam_poc" in self.fields:
            self.fields["tam_poc"].queryset = User.objects.filter(is_staff=True, email__icontains="@rdash.io")
        if "poc" in self.fields:
            self.fields["poc"].queryset = User.objects.filter(org_id=kwargs["instance"].organization_id)
        country = Country.objects.filter(
            id=Organization.objects.get(id=kwargs["instance"].organization_id).country_id
        ).first()
        if "timezone" in self.fields:
            self.initial["timezone"] = (
                CountryTimezoneMapping.objects.filter(country_id=country.pk, is_default=True).first().timezone
            )
        if "currency" in self.fields:
            self.initial["currency"] = (
                CountryCurrencyMapping.objects.filter(country_id=country.pk, is_default=True).first().currency
            )
        if "tax_type" in self.fields:
            self.initial["tax_type"] = (
                CountryTaxMapping.objects.filter(country_id=country.pk, is_default=True).first().tax_type
            )


class OrganizationForeignKeyModelForm(forms.ModelForm):
    class Meta:
        widgets = {
            # "organization": apply_select2(forms.Select),
            "role": apply_select2(forms.Select),
            "city": apply_select2(forms.Select),
            "state": apply_select2(forms.Select),
            "sales_poc": apply_select2(forms.Select),
            "tam_poc": apply_select2(forms.Select),
        }
        model = OrganizationConfig
        exclude = (
            "poc",
            "order_cc_emails",
            "order_receiver_emails",
            "domain_name",
            "timezone",
            "currency",
            "tax_type",
            "metabase_dashboard_id",
            "order_flow",
            "use_client_code",
            "block_all_actions",
            "is_phone_number_login_enabled",
            "embed_images_in_snag_excel_export",
        )

    def __init__(self, *args, **kwargs):
        super(OrganizationForeignKeyModelForm, self).__init__(*args, **kwargs)
        if "city" in self.fields:
            self.fields["city"].queryset = City.objects.all().select_related("country", "state")
        if "state" in self.fields:
            self.fields["state"].queryset = State.objects.all().select_related("country")
        if "sales_poc" in self.fields:
            self.fields["sales_poc"].queryset = User.objects.filter(is_staff=True, email__icontains="@rdash.io")
        if "tam_poc" in self.fields:
            self.fields["tam_poc"].queryset = User.objects.filter(is_staff=True, email__icontains="@rdash.io")


class RolePermissionsInlineForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if "initial" in kwargs:
            self.has_changed = lambda: True

    class Meta:
        widgets = {
            "permission": apply_select2(forms.Select),
        }
        model = RolePermission
        exclude = ()


class OrganizationConfigPermissionsInlineForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if "initial" in kwargs:
            self.has_changed = lambda: True

    class Meta:
        widgets = {
            "permission": apply_select2(forms.Select),
        }
        model = OrganizationConfigCorePermission
        exclude = ()


class FromToOrgMappingModelFormAbstract(forms.ModelForm):
    vendor_status = forms.ChoiceField(choices=VendorStatusChoices.choices)

    def clean(self):
        cleaned_data = super().clean()
        if not _Vendor.objects.filter(pk=cleaned_data.get("org_to").pk).exists():
            raise forms.ValidationError("Invalid Vendor for From To Org Mapping. Vendor data entry not found.")
        return cleaned_data


class FromToOrgMappingModelForm(FromToOrgMappingModelFormAbstract):
    class Meta:
        widgets = {
            "org_from": apply_select2(forms.Select),
            "org_to": apply_select2(forms.Select),
            "invited_by_org": apply_select2(forms.Select),
        }
        model = FromToOrgMapping
        exclude = ("deleted_by", "deleted_at", "updated_by", "updated_at", "vendor_poc", "is_active")

    def __init__(self, *args, **kwargs):
        super(FromToOrgMappingModelForm, self).__init__(*args, **kwargs)
        if "org_to" in self.fields:
            self.fields["org_to"].queryset = Vendor.objects.all().prefetch_related("vendor").order_by("name")

    def clean(self):
        super(FromToOrgMappingModelForm, self).clean()


class FromToOrgMappingEditModelForm(FromToOrgMappingModelFormAbstract):
    class Meta:
        widgets = {
            "org_from": apply_select2(forms.Select),
            "org_to": apply_select2(forms.Select),
            "invited_by_org": apply_select2(forms.Select),
        }
        model = FromToOrgMapping
        exclude = ("deleted_at",)

    def clean(self):
        super(FromToOrgMappingEditModelForm, self).clean()


class PmcVendorMappingModelForm(forms.ModelForm):
    class Meta:
        widgets = {
            "vendor": apply_select2(forms.Select),
            "pmc": apply_select2(forms.Select),
        }

    def __init__(self, *args, **kwargs):
        super(PmcVendorMappingModelForm, self).__init__(*args, **kwargs)
        if "pmc" in self.fields:
            self.fields["pmc"].queryset = PMC.objects.all().order_by("name")
        if "vendor" in self.fields:
            self.fields["vendor"].queryset = Vendor.objects.all().prefetch_related("vendor").order_by("name")


class UserCreationForm(_UserCreationForm):
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        password = User.objects.make_random_password()
        self.fields["password1"].required = False
        self.fields["password2"].required = False
        self.fields["first_name"].required = True
        self.fields["last_name"].required = True
        self.fields["password1"].widget.attrs["value"] = password
        self.fields["password2"].widget.attrs["value"] = password
        self.fields["password1"].widget.attrs["hidden"] = True
        self.fields["password2"].widget.attrs["hidden"] = True
        self.fields["is_verified"].initial = True


class OrganizationConfigRoleModelForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super(OrganizationConfigRoleModelForm, self).__init__(*args, **kwargs)

    class Meta:
        model = OrganizationConfigRole
        fields = "__all__"


class ProjectOrganizationModelForm(forms.ModelForm):
    class Meta:
        widgets = {
            "organization": apply_select2(forms.Select),
            "project": apply_select2(forms.Select),
            "role": apply_select2(forms.Select),
            "assigned_by": apply_select2(forms.Select),
        }

    def clean(self):
        cleaned_data = super().clean()
        if not ProjectOrganizationHelper.is_org_assignment_permitted(
            project_id=cleaned_data.get("project").pk,
            to_assign_org=cleaned_data.get("organization").pk,
            assigned_by_org=cleaned_data.get("assigned_by").pk,
        ):
            raise ValidationError("Given Organization already assigned on selected project")

        return cleaned_data


class VendorDataInlineFormSet(forms.models.BaseInlineFormSet):
    def _construct_form(self, i, **kwargs):
        form = super()._construct_form(i, **kwargs)
        if form.instance.pk is None:
            form.fields["serial_number"].widget.attrs["value"] = VendorServiceV2.fetch_next_serial_number()
            form.fields["serial_number"].widget.attrs["hidden"] = True
        return form

    def get_readonly_fields(self):
        return [field.name for field in self.model._meta.fields]


class OrganizationCustomFieldConfigForm(forms.ModelForm):
    class Meta:
        widgets = {
            "org": apply_select2(forms.Select),
        }


class ElementCategoryMappingForm(forms.ModelForm):
    def clean(self) -> dict[str, Any]:
        data = super().clean()
        category = data.get("category")
        if category.type == ElementCategory.CategoryScopeChoices.LOCAL_SCOPE.value and data.get("organization") is None:
            raise forms.ValidationError("Organization must be assigned to local scope category.")
        if category.type == ElementCategory.CategoryScopeChoices.GLOBAL_SCOPE.value:
            raise forms.ValidationError(
                "In case of Global scope category, there is no need to create category org mapping"
            )
        return data

    class Meta:
        widgets = {
            "category": apply_select2(forms.Select),
        }


class GstSlabForm(forms.ModelForm):
    class Meta:
        model = GstSlab
        fields = ("gst_percent", "is_active")


class RegionForm(forms.ModelForm):
    class Meta:
        widgets = {
            "organization": apply_select2(forms.Select),
        }


class ElementCategoryForm(forms.ModelForm):
    class Meta:
        fields = "__all__"
        widgets = {
            "organization": apply_select2(forms.Select),
        }


class RegionInlineForm(forms.ModelForm):
    def clean(self):
        try:
            data = super().clean()
        except ValidationError:
            raise ValidationError("This state belongs to another region. Remove state from another region.")
        return data

    class Meta:
        widgets = {
            "state": apply_select2(forms.Select),
        }


class ProductionDrawingTagForm(forms.ModelForm):
    class Meta:
        model = ProductionDrawingTag
        fields = (
            "name",
            "is_active",
        )


class UnitOfMeasurementForm(forms.ModelForm):
    def clean(self):
        cleaned_data = super().clean()
        if cleaned_data.get("name") in UnitOfMeasurementCache.get().values():
            raise forms.ValidationError("Unit of Measurement already exists.")
        return cleaned_data

    class Meta:
        model = UnitOfMeasurement
        fields = ("name",)


class GlobalDesignFileTagTagForm(forms.ModelForm):
    class Meta:
        model = DesignFileTag
        fields = ("name",)


class VendorDataForm(forms.ModelForm):
    code = forms.CharField(max_length=10, required=False)
    city = forms.CharField(required=False, max_length=50)
    state = forms.ChoiceField(choices=StateChoices.choices)
    msme_id = forms.CharField(required=False, max_length=100)
    aadhar_number = forms.CharField(required=False, max_length=100)
    referral_organization = forms.ModelChoiceField(
        queryset=Organization.objects.all(),
        empty_label="---------",
        required=False,
    )
    created_by = forms.ModelChoiceField(
        queryset=User.objects.all(),
        to_field_name="pk",
        required=False,  # Change to False if you want to make it optional
    )

    class Meta:
        model = VendorData
        fields = (
            "code",
            "city",
            "state",
            "msme_id",
            "aadhar_number",
            "referal_organization",
            "created_by",
        )


class OrganizationGSTNumberDataForm(forms.ModelForm):
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.fields["gst_number"].required = True

    class Meta:
        model = OrganizationGSTNumberData
        fields = (
            "gst_number",
            "gst_state",
            "name",
            "file",
            "created_by",
        )


class OrganizationAddressForm(forms.ModelForm):
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        if "city" in self.fields:
            self.fields["city"].queryset = City.objects.all().select_related("country", "state")
        if "state" in self.fields:
            self.fields["state"].queryset = State.objects.all().select_related("country")

    class Meta:
        model = OrganizationAddress
        fields = (
            "header",
            "address_line_1",
            "address_line_2",
            "zip_code",
            "city",
            "state",
            "country",
        )
        widgets = {
            "city": apply_select2(forms.Select),
            "state": apply_select2(forms.Select),
            "country": apply_select2(forms.Select),
        }


class OrganizationDocumentForm(forms.ModelForm):
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.fields["type"].required = True
        self.fields["file"].required = True

    class Meta:
        model = OrganizationDocument
        fields = (
            "name",
            "type",
            "file",
            "uploaded_by",
        )


class ClientDataForm(forms.ModelForm):
    code = forms.CharField(required=False, max_length=5, initial="RDASH")

    class Meta:
        model = ClientData
        fields = ("code",)


class OrgForm(forms.ModelForm):
    vendor_code = forms.CharField(max_length=10, required=False)
    client_code = forms.CharField(required=False, max_length=5, initial="RDASH")
    # state = forms.ModelChoiceField(
    #     queryset=State.objects.select_related("country").filter(is_active=True), widget=apply_select2(forms.Select)
    # )

    created_by = forms.CharField(
        disabled=True,
        required=False,
    )
    updated_by = forms.CharField(
        disabled=True,
        required=False,
    )

    creation_time = forms.DateTimeField(disabled=True, required=False)
    updation_time = forms.DateTimeField(disabled=True, required=False)

    # city = forms.ModelChoiceField(
    #     queryset=City.objects.select_related("country", "state").filter(is_active=True),
    #     widget=apply_select2(forms.Select),
    # )
    is_active = forms.BooleanField(
        required=False,
        help_text="Marking Organization Inactive will log out all its users. Unselect this instead of deleting accounts.",  # noqa
        label="Active",
        initial=True,
    )

    def __init__(self, *args, **kwargs):
        super(OrgForm, self).__init__(*args, **kwargs)
        # self.fields["city"].choices = self.get_city_choices()

    def get_city_choices(self):
        city_mappings = CityStateMapping.objects.all()
        choices = [(mapping.city, mapping.city) for mapping in city_mappings]
        choices.insert(0, ("", "Not set"))
        return choices

    def clean_client_code(self):
        client_code = self.cleaned_data.get("client_code")
        client_code = client_code.upper() if client_code else None
        if not client_code:
            raise ValidationError(_("Client code is required."))
        if len(client_code) not in [4, 5]:
            raise ValidationError(_("Client code must be of 5 characters."))
        if not re.match(RegularExpression.CLIENT_CODE_CONTROLROOM, client_code):
            raise ValidationError(_("The Client should have alphabets only."))

        if (
            client_code not in ["RDASH", "PROJ"]
            and ClientData.objects.exclude(pk=self.instance.pk).filter(code=client_code).exists()
        ):
            raise ValidationError(_("Client code already exists."))

        return client_code

    class Meta:
        model = Organization
        fields = (
            "name",
            "logo",
            # "pan_number",
            "referral_org",
            "referral_by",
            "country",
            # "city",
            # "state",
            # "msme_id",
            # "aadhar_number",
            "created_by",
            "creation_time",
            "updation_time",
            "updated_by",
        )
        widgets = {
            "state": apply_select2(forms.Select),
            "city": apply_select2(forms.Select),
        }


class DashboardTagsForm(forms.ModelForm):
    class Meta:
        widgets = {
            "tag": apply_select2(forms.Select),
        }


class CustomDashboardCollectionForm(forms.ModelForm):
    class Meta:
        model = CustomDashboardCollection
        widgets = {
            "organization": apply_select2(forms.Select),
        }
        fields = ("name", "custom_question_id", "organization")


class OrganizationCallbackForm(forms.ModelForm):
    class Meta:
        widgets = {
            "organization": apply_select2(forms.Select),
        }


class TimezoneAdminForm(forms.ModelForm):
    class Meta:
        widgets = {
            "tz": apply_select2(forms.Select),
        }


class CountryAdminForm(forms.ModelForm):
    class Meta:
        widgets = {
            "timezone": apply_select2(forms.Select),
        }


class FacebookPageCrmBoardMappingForm(forms.ModelForm):
    organization_id = forms.IntegerField(
        required=True, label="Organization ID", widget=forms.NumberInput(attrs={"placeholder": "Enter Organization ID"})
    )

    class Meta:
        widgets = {
            "page": apply_select2(forms.Select),
            "board": apply_select2(forms.Select),
            "stage": apply_select2(forms.Select),
        }
        model = FacebookPageCrmBoardMapping
        fields = ["organization_id", "page", "board", "stage", "created_by"]

    def __init__(self, *args, **kwargs):
        super(FacebookPageCrmBoardMappingForm, self).__init__(*args, **kwargs)
        self.fields["page"].queryset = FacebookPage.objects.none()
        self.fields["board"].queryset = Board.objects.none()
        self.fields["stage"].queryset = BoardStageProxy.objects.none()

        if "organization_id" in self.data:
            try:
                org_id = int(self.data.get("organization_id"))
                if Organization.objects.filter(id=org_id).exists():
                    self.fields["page"].queryset = FacebookPage.objects.filter(
                        facebook_connection__organization_id=org_id,
                        facebook_connection__is_active=True,
                        deleted_at__isnull=True,
                        facebook_crm_page__isnull=True,
                    ).order_by("name")
                    self.fields["board"].queryset = Board.objects.filter(
                        organization_id=org_id, deleted_at__isnull=True
                    ).order_by("name")
                    self.fields["stage"].queryset = BoardStageProxy.objects.filter(
                        deleted_at__isnull=True, board__organization_id=org_id
                    ).order_by("name")
                else:
                    self.fields["page"].queryset = FacebookPage.objects.none()
                    self.fields["board"].queryset = Board.objects.none()
                    self.fields["stage"].queryset = BoardStageProxy.objects.none()
            except (ValueError, TypeError):
                pass

    def clean(self):
        super(FacebookPageCrmBoardMappingForm, self).clean()
