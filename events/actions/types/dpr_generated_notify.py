import structlog

from authorization.domain.constants import Actions
from common.events.constants import Events
from events.actions.base_action_processor import BaseActionProcessor
from events.actions.work_progress_action_processor import WorkProgressNotifyActionProcessorV2
from events.domain.constants import PushNotificationMode
from external_services.messengers.whatsapp.entities import EventTemplates
from microcontext.choices import TemplateContextChoices
from rollingbanners.hash_id_converter import HashIdConverter

logger = structlog.getLogger(__name__)


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_WORK_PROGRESS_REPORT_PDF_GENERATED)
class ProgressReportCreationActionProcessor(WorkProgressNotifyActionProcessorV2):
    _push_notification_template = TemplateContextChoices.PROGRESS_REPORT_CREATED
    _push_notification_mode = PushNotificationMode.GROUP
    _whatsapp_template = EventTemplates.PROGRESS_REPORT_CREATED.value
    _event = Events.WORK_PROGRESS_REPORT_PDF_GENERATED
    _log_creation = True

    def get_redirect_link(self) -> str:
        return f"project/{HashIdConverter.encode(self.get_action_data().project_id)}/work-report/progress-report"


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_WORK_PROGRESS_SUBSCRIBERS_REPORT_PDF_GENERATED)
class WorkProgressSubscribersReportPDFGeneratedActionProcessor(ProgressReportCreationActionProcessor):
    _push_notification_template = TemplateContextChoices.PROGRESS_REPORT_CREATED
    _push_notification_mode = PushNotificationMode.GROUP
    _whatsapp_template = EventTemplates.PROGRESS_REPORT_CREATED_SUBSCRIBERS.value
    _event = Events.WORK_PROGRESS_REPORT_PDF_GENERATED
    _log_creation = True

    def get_action(self):
        return Actions.NOTIFY_WORK_PROGRESS_REPORT_PDF_GENERATED

    def get_whatsapp_data(self) -> dict:
        prepared_data = self.get_prepared_data()
        return {
            "project": f"{prepared_data.get('project').name} ",
            "project_user": prepared_data.get("creator").name,
            "org": prepared_data.get("creator").org.name,
            "job_id": prepared_data.get("project").job_id,
        }


@BaseActionProcessor.register_action_processor(action=Actions.NOTIFY_CLIENT_FOR_WORK_PROGRESS_REPORT_PDF_GENERATED)
class VendorProgressReportCreationActionProcessorV2(WorkProgressNotifyActionProcessorV2):
    _push_notification_template = TemplateContextChoices.VENDOR_PROGRESS_REPORT_CREATED
    _whatsapp_template = EventTemplates.VENDOR_PROGRESS_REPORT_CREATED.value
    _event = Events.WORK_PROGRESS_REPORT_PDF_GENERATED
    _push_notification_mode = PushNotificationMode.GROUP
    _log_creation = True

    def get_recipient_org_id(self) -> int:
        from project.domain.services import get_immediate_client_id

        action_data = self.get_action_data()
        return get_immediate_client_id(project_id=action_data.project_id, organization_id=action_data.org_id)

    def get_whatsapp_data(self) -> dict:
        data = super().get_whatsapp_data()
        prepared_data = self.get_prepared_data()
        data["vendor_org"] = prepared_data.get("creator").org.name
        return data

    def get_redirect_link(self) -> str:
        return f"project/{HashIdConverter.encode(self.get_action_data().project_id)}/orders/vendor-wise-progress-report"


@BaseActionProcessor.register_action_processor(
    action=Actions.NOTIFY_CLIENT_SUBSCRIBERS_FOR_WORK_PROGRESS_REPORT_PDF_GENERATED
)
class VendorProgressReportSubscribersActionProcessorV2(VendorProgressReportCreationActionProcessorV2):
    _push_notification_template = TemplateContextChoices.VENDOR_PROGRESS_REPORT_CREATED
    _whatsapp_template = EventTemplates.VENDOR_PROGRESS_REPORT_CREATED_SUBSCRIBERS.value
    _event = Events.WORK_PROGRESS_REPORT_PDF_GENERATED
    _push_notification_mode = PushNotificationMode.GROUP
    _log_creation = True

    def get_action(self):
        return Actions.NOTIFY_CLIENT_FOR_WORK_PROGRESS_REPORT_PDF_GENERATED

    def get_whatsapp_data(self) -> dict:
        prepared_data = self.get_prepared_data()
        return {
            "project": f"{prepared_data.get('project').name} ",
            "vendor_org": prepared_data.get("creator").org.name,
            "org": prepared_data.get("creator").org.name,
            "job_id": prepared_data.get("project").job_id,
        }
