from authorization.domain.constants import Actions
from common.events import Events

EventActionMapping = {
    Events.RECCE_LINK_CREATED: (
        Actions.NOTIFY_RECCE_LINK_CREATION,
        Actions.NOTIFY_RECCE_ASSIGNED,
    ),
    Events.RECCE_SUBMITTED: (
        Actions.NOTIFY_RECCE_SUBMISSION,
        Actions.NOTIFY_RECCE_SUBMISSION_SELF,
        Actions.CALLBACK_RECCE_SUBMITTED,
        Actions.WEBHOOK_RECCE_SUBMITTED,
    ),
    Events.RECCE_STARTED: (Actions.NOTIFY_RECCE_STARTED,),
    Events.PROJECT_COMMENT_MENTIONED: (
        Actions.NOTIFY_COMMENT_MENTIONED,
        Actions.NOTIFY_PROJECT_COMMENT_MENTIONED,
    ),
    Events.COMMENT_APPROVAL_REQUESTED: (
        Actions.NOTIFY_COMMENT_APPROVAL_REQUESTED,
        Actions.NOTIFY_COMMENT_APPROVAL_MENTIONED,
    ),
    Events.COMMENT_APPROVAL_ACCEPTED_REJECTED: (Actions.NOTIFY_COMMENT_APPROVAL_ACCEPTED_REJECTED,),
    Events.PROJECT_CREATED: (
        Actions.NOTIFY_PROJECT_CREATION,
        Actions.CALLBACK_PROJECT_CREATED,
        Actions.WEBHOOK_PROJECT_CREATED,
    ),
    Events.PROJECT_ASSIGNED: (
        Actions.NOTIFY_PROJECT_ASSIGNMENT,
        Actions.WEBHOOK_PROJECT_ASSIGNMENT,
    ),
    Events.PROJECT_SHARED: (Actions.NOTIFY_PROJECT_SHARED,),
    Events.PROJECT_USER_REMOVED: (Actions.NOTIFY_PROJECT_USER_REMOVED,),
    Events.PROJECT_DATE_CHANGED: (Actions.NOTIFY_PROJECT_DATE_CHANGED,),
    Events.PROJECT_DATE_ASSIGN: (Actions.NOTIFY_PROJECT_DATE_ASSIGN,),
    Events.RECCE_UPDATED: (Actions.NOTIFY_RECCE_UPDATED,),
    Events.RECCE_APPROVED: (Actions.NOTIFY_RECCE_APPROVED,),
    ##
    Events.ORDER_CREATED: (
        Actions.CALLBACK_ORDER_CREATED,
        Actions.WEBHOOK_ORDER_CREATED,
    ),
    Events.ORDER_SENT_WITHOUT_NOTFICATION: (  ## Typo?
        Actions.CALLBACK_ORDER_SENT,
        Actions.WEBHOOK_ORDER_SENT_WITHOUT_NOTIFICATION,
    ),
    Events.ORDER_SENT: (
        Actions.NOTIFY_ORDER_SENT,
        Actions.NOTIFY_ORDER_RECEIEVED,
        Actions.CALLBACK_ORDER_SENT,
        Actions.WEBHOOK_ORDER_SENT,
    ),
    Events.ORDER_MODIFIED: (
        Actions.NOTIFY_ORDER_SENT_MODIFIED,
        Actions.NOTIFY_ORDER_RECEIVED_MODIFIED,
        Actions.CALLBACK_ORDER_MODIFIED,
        Actions.WEBHOOK_ORDER_MODIFIED,
    ),
    Events.DRAFT_ORDER_MODIFIED: (
        Actions.CALLBACK_ORDER_MODIFIED,
        Actions.WEBHOOK_DRAFT_ORDER_MODIFIED,
    ),
    Events.ORDER_CANCELLED: (
        Actions.NOTIFY_ORDER_RECEIVED_CANCELLED,
        Actions.NOTIFY_ORDER_SENT_CANCELLED,
        Actions.CALLBACK_ORDER_CANCELLED,
        Actions.WEBHOOK_ORDER_CANCELLED,
    ),
    Events.PO_UPLOADED: (
        Actions.NOTIFY_PO_RECEIVED,
        Actions.NOTIFY_PO_SENT,
        Actions.CALLBACK_PO_UPLOADED,
        Actions.WEBHOOK_PO_UPLOADED,
    ),
    Events.PO_REVISED: (
        Actions.CALLBACK_PO_REVISED,
        Actions.WEBHOOK_PO_REVISED,
    ),
    Events.ORDER_COMPLETED: (Actions.NOTIFY_ORDER_RECEIEVED_COMPLETED, Actions.NOTIFY_ORDER_SENT_COMPLETED),
    Events.DESIGN_APPROVED: (Actions.NOTIFY_DESIGN_APPROVED,),
    Events.DESIGN_FREEZED: (
        Actions.NOTIFY_DESIGN_FREEZE,
        Actions.WEBHOOK_DESIGN_FREEZE,
    ),
    Events.PO_UPLOADED_EMAIL: (Actions.NOTIFY_PO_UPLOADED_EMAIL,),
    Events.PO_REVISED_EMAIL: (Actions.NOTIFY_PO_REVISED_EMAIL,),
    Events.PO_CANCELLED_EMAIL: (Actions.NOTIFY_PO_CANCELLED_EMAIL,),
    Events.PO_CANCELLED: (Actions.NOTIFY_PO_SENT_CANCELLED, Actions.NOTIFY_PO_RECEIVED_CANCELLED),
    Events.NEW_INVOICE_UPLOADED: (
        Actions.NOTIFY_NEW_INVOICE_UPLOADED,
        Actions.WEBHOOK_NEW_INVOICE_UPLOADED,
    ),
    Events.ALL_INVOICES_MARKED_UPLOADED: (Actions.NOTIFY_ALL_INVOICES_MARKED_UPLOADED,),
    Events.PROJECT_COMMENT: (Actions.NOTIFY_PROJECT_COMMENT,),
    Events.WORK_PROGRESS_MARK_EXECUTION_COMPLETED: (
        Actions.NOTIFY_MARK_EXECUTION_COMPLETED,
        Actions.WEBHOOK_MARK_EXECUTION_COMPLETED,
    ),
    ##
    Events.WORK_PROGRESS_REPORT_GENERATED: (
        Actions.TEMPLATE_WORK_PROGRESS_GENERATE_REPORT,
        Actions.CALLBACK_DPR_CREATED,
    ),
    Events.WORK_PROGRESS_REPORT_PDF_GENERATED: (
        Actions.NOTIFY_WORK_PROGRESS_REPORT_PDF_GENERATED,
        Actions.NOTIFY_CLIENT_FOR_WORK_PROGRESS_REPORT_PDF_GENERATED,
        Actions.NOTIFY_CLIENT_SUBSCRIBERS_FOR_WORK_PROGRESS_REPORT_PDF_GENERATED,
        Actions.NOTIFY_WORK_PROGRESS_SUBSCRIBERS_REPORT_PDF_GENERATED,
        Actions.WEBHOOK_DPR_CREATED,
    ),
    Events.WORK_PROGRESS_EXPORT_REPORT_GENERATED: (Actions.TEMPLATE_WORK_PROGRESS_EXPORT_REPORT,),
    Events.LEAD_EXPORT_EXCEL_GENERATED: (Actions.LEAD_EXCEL_EXPORT,),
    Events.WORK_PROGRESS_EXPORT_REPORT_PDF_GENERATED: (Actions.NOTIFY_WORK_PROGRESS_EXPORT_REPORT_PDF_GENERATED,),
    Events.PROPOSAL_SENT: (Actions.NOTIFY_PROPOSAL_SENT,),
    Events.PROPOSAL_REJECTED: (Actions.NOTIFY_PROPOSAL_REJECTED,),
    Events.DESIGN_NEW_VERSION_UPLOADED: (
        Actions.NOTIFY_DESIGN_NEW_VERSION_UPLOADED,
        Actions.CALLBACK_DESIGN_NEW_VERSION_UPLOADED,
        Actions.WEBHOOK_DESIGN_NEW_VERSION_UPLOADED,
    ),
    Events.DESIGN_NEW_VERSION_POST_FREEZE_UPLOADED: (Actions.NOTIFY_DESIGN_NEW_VERSION_POST_FREEZE_UPLOADED,),
    Events.PROPOSAL_REQUEST_FOR_CHANGE: (Actions.NOTIFY_PROPOSAL_REQUEST_FOR_NEW_ORDER,),
    Events.PROPOSAL_REQUEST_FOR_ORDER_CHANGE: (Actions.NOTIFY_PROPOSAL_REQUEST_FOR_ORDER_CHANGE,),
    Events.PROPOSAL_APPROVE_FOR_NEW_ORDER: (
        Actions.NOTIFY_PROPOSAL_APPROVE_FOR_NEW_ORDER,
        Actions.CALLBACK_PROPOSAL_APPROVED,
        Actions.WEBHOOK_PROPOSAL_APPROVED,
    ),
    Events.PROPOSAL_APPROVE_FOR_ORDER_CHANGE: (
        Actions.NOTIFY_PROPOSAL_APPROVE_FOR_ORDER_CHANGE,
        Actions.CALLBACK_PROPOSAL_APPROVED,
        Actions.WEBHOOK_PROPOSAL_APPROVED,
    ),
    Events.PROPOSAL_REJECT_FOR_NEW_ORDER: (
        Actions.NOTIFY_PROPOSAL_REJECT_FOR_NEW_ORDER,
        Actions.CALLBACK_PROPOSAL_REJECTED,
        Actions.WEBHOOK_PROPOSAL_REJECTED,
    ),
    Events.PROPOSAL_REJECT_FOR_ORDER_CHANGE: (
        Actions.NOTIFY_PROPOSAL_REJECT_FOR_ORDER_CHANGE,
        Actions.CALLBACK_PROPOSAL_REJECTED,
        Actions.WEBHOOK_PROPOSAL_REJECTED,
    ),
    Events.ORDER_SENT_WITHOUT_PROPOSAL: (Actions.NOTIFY_ORDER_SENT_WITHOUT_PROPOSAL,),
    Events.ORDER_MODIFY_WITHOUT_PROPOSAL: (Actions.NOTIFY_ORDER_MODIFY_WITHOUT_PROPOSAL,),
    Events.ORDER_CANCEL_WITHOUT_PROPOSAL: (Actions.NOTIFY_ORDER_CANCEL_WITHOUT_PROPOSAL,),
    Events.TASK_CREATED: (
        Actions.CALLBACK_TASK_CREATED,
        Actions.NOTIFY_TASK_ASSIGNED,
        Actions.NOTIFY_TASK_MENTIONED,
        Actions.WEBHOOK_TASK_CREATED,
    ),
    Events.TASK_UPDATED: (Actions.NOTIFY_TASK_MENTIONED, Actions.NOTIFY_TASK_ASSIGNED, Actions.NOTIFY_TASK_UPDATED),
    Events.TASK_REPLIED: (Actions.NOTIFY_TASK_REPLY_MENTIONED, Actions.NOTIFY_APPROVAL_REQUEST_REPLY_MENTIONED),
    Events.TASK_DONE: (
        Actions.CALLBACK_TASK_DONE,
        Actions.NOTIFY_TASK_DONE,
        Actions.WEBHOOK_TASK_DONE,
    ),
    Events.TASK_REMINDER: (Actions.NOTIFY_TASK_REMINDER,),
    Events.TODAYS_DUE_TASK_COUNT: (Actions.NOTIFY_TODAYS_DUE_TASK_COUNT,),
    Events.TASK_ARCHIVED_FOR_ALL: (Actions.NOTIFY_TASK_ARCHIVED_FOR_ALL,),
    Events.USER_CREATED: (
        Actions.CALLBACK_USER_CREATED,
        Actions.WEBHOOK_USER_CREATED,
    ),
    Events.USER_LOGGED_IN: (Actions.WEBHOOK_USER_LOGGED_IN,),
    Events.USER_ONBOARDED: (Actions.NOTIFY_USER_ONBOARDED,),
    Events.SINGLE_RECCE_APPROVED: (Actions.CALLBACK_SINGLE_RECCE_APPROVED,),
    Events.DESIGN_FILE_UPLOADED: (
        Actions.CALLBACK_DESIGN_FILE_UPLOAD,
        Actions.WEBHOOK_DESIGN_FILE_UPLOAD,
    ),
    Events.DESIGN_FILE_STATUS_UPDATED: (Actions.WEBHOOK_DESIGN_FILE_STATUS_UPDATED,),
    Events.SNAG_POC_ALLOTED: (
        Actions.CALLBACK_SNAG_POC_ALLOTED,
        Actions.WEBHOOK_SNAG_POC_ALLOTED,
    ),
    Events.HIERARCHY_RESET: (Actions.SCHEDULE_MISCONFIG_REQUEST,),
    Events.SKIP_MISCONFIGURED_LEVELS: (Actions.SKIP_REQUESTS_MISCONFIGURED_LEVELS,),
    Events.SNAG_ASSIGNED: (
        Actions.CALLBACK_SNAG_ASSIGNED,
        Actions.NOTIFY_SNAG_ASSIGNMENT_TO_CREATOR,
        Actions.NOTIFY_SNAG_ASSIGNMENT_TO_ASSIGNEE,
        Actions.WEBHOOK_SNAG_ASSIGNED,
    ),
    Events.SNAG_BULK_ASSIGNED: (
        Actions.CALLBACK_SNAG_ASSIGNED,
        Actions.NOTIFY_SNAG_BULK_ASSIGNMENT_TO_CREATORS,
        Actions.NOTIFY_SNAG_BULK_ASSIGNMENT_TO_ASSIGNEE,
    ),
    Events.DESIGN_FILE_APPROVED: (
        Actions.DESIGN_FILE_APPROVED,
        Actions.WEBHOOK_DESIGN_FILE_APPROVED,
    ),
    Events.RESOURCE_CANCEL: (Actions.RESOURCE_REQUEST_CANCEL,),
    Events.APPROVAL_REQUEST_CREATED: (
        Actions.CALLBACK_APPROVAL_REQUEST_CREATED,
        Actions.NOTIFY_APPROVAL_REQUEST_CREATED,
        Actions.WEBHOOK_APPROVAL_REQUEST_CREATED,
    ),
    Events.APPROVAL_REQUEST_APPROVED: (
        Actions.CALLBACK_APPROVAL_REQUEST_APPROVED,
        Actions.NOTIFY_APPROVAL_REQUEST_APPROVED,
        Actions.WEBHOOK_APPROVAL_REQUEST_APPROVED,
    ),
    Events.APPROVAL_REQUEST_FINALLY_APPROVED: (
        Actions.CALLBACK_APPROVAL_REQUEST_FINALIZED,
        Actions.NOTIFY_APPROVAL_REQUEST_FINALLY_APPROVED,
    ),
    Events.APPROVAL_REQUEST_EDITED: (
        Actions.CALLBACK_APPROVAL_REQUEST_EDITED,
        Actions.NOTIFY_APPROVAL_REQUEST_EDITED,
        Actions.WEBHOOK_APPROVAL_REQUEST_EDITED,
    ),
    Events.APPROVAL_REQUEST_REJECTED: (
        Actions.CALLBACK_APPROVAL_REQUEST_FINALIZED,
        Actions.NOTIFY_APPROVAL_REQUEST_REJECTED,
    ),
    Events.APPROVAL_REQUEST_HOLD: (Actions.NOTIFY_APPROVAL_REQUEST_HOLD,),
    Events.BOQ_ELEMENT_PROGRESS_PERCENTAGE_UPDATE: (
        Actions.CALLBACK_BOQ_ELEMENT_PROGRESS_PERCENTAGE_UPDATE,
        Actions.WEBHOOK_BOQ_ELEMENT_PROGRESS_PERCENTAGE_UPDATE,
    ),
    Events.ORGANIZATION_CREATED: (
        # Actions.CALLBACK_ORGANIZATION_CREATED,
        Actions.WEBHOOK_ORGANIZATION_CREATED,
    ),
    Events.STOCK_TRANSFER_BATCH_CREATED: (Actions.NOTIFY_STOCK_TRANSFER_BATCH_CREATED,),
    Events.STOCK_TRANSFER_BATCH_ACTION: (Actions.NOTIFY_STOCK_TRANSFER_BATCH_ACTION,),
    Events.PROPOSAL_CREATED: (
        Actions.CALLBACK_PROPOSAL_CREATED,
        Actions.WEBHOOK_PROPOSAL_CREATED,
    ),
    Events.PROPOSAL_UPDATED_WHEN_ORDER_EXIST: (Actions.WEBHOOK_PROPOSAL_UPDATED_WHEN_ORDER_EXIST,),
    Events.PROPOSAL_UPDATED_WHEN_ORDER_NOT_EXIST: (Actions.WEBHOOK_PROPOSAL_UPDATED_WHEN_ORDER_NOT_EXIST,),
    Events.PAYMENT_ENTRY_CREATED: (
        Actions.CALLBACK_PAYMENT_ENTRY_CREATED,
        Actions.WEBHOOK_PAYMENT_ENTRY_CREATED,
    ),
    Events.SNAG_ALLOTTED_POC_AND_COMMITTED_TIMELINE: (
        Actions.NOTIFY_SNAG_ALLOT_AND_COMMIT_TIMELINE_TO_ALLOTEE,
        Actions.NOTIFY_SNAG_ALLOT_AND_COMMIT_TIMELINE_TO_OBSERVERS,
    ),
    Events.SNAG_UNRESOLVED: (Actions.NOTIFY_SNAG_UNRESOLVED,),
    Events.SNAG_BULK_ALLOTTED_AND_COMMITTED_TIMELINE: (
        Actions.NOTIFY_SNAG_BULK_ALLOT_AND_TIMELINE_COMMIT_TO_ALLOTEE,
        Actions.NOTIFY_SNAG_BULK_ALLOT_AND_COMMIT_TIMELINE_TO_OBSERVERS,
    ),
    Events.LEAD_ASSIGNED: (
        Actions.NOTIFY_LEAD_ASSIGNMENT,
        Actions.WEBHOOK_LEAD_ASSIGNMENT,
    ),
    Events.BOARD_ASSIGNED: (Actions.NOTIFY_BOARD_ASSIGNMENT,),
    Events.LEAD_QUOTATION_SUBMITTED: (
        Actions.CALLBACK_LEAD_QUOTATION_SUBMITTED,
        Actions.WEBHOOK_LEAD_QUOTATION_SUBMITTED,
    ),
    Events.LEAD_STAGE_CHANGED: (Actions.WEBHOOK_LEAD_STAGE_CHANGED,),
    Events.STOCK_TRANSFER_BATCH_APPROVED: (Actions.CALLBACK_STOCK_TRANSFER_BATCH_APPROVED,),
    Events.PROJECT_POC_ASSIGNED: (Actions.CALLBACK_UPDATE_POC_ROLE,),
    Events.PROJECT_SCHEDULE_COMPLETED: (Actions.NOTIFY_PROJECT_SCHEDULE_COMPLETED,),
    Events.PROJECT_SCHEDULE_DELAY: (Actions.NOTIFY_PROJECT_SCHEDULE_DELAY,),
    Events.PROJECT_SCHEDULE_OVERDUE: (Actions.NOTIFY_PROJECT_SCHEDULE_OVERDUE,),
    Events.PROJECT_SCHEDULE_ACTIVITIES_DELETED: (Actions.NOTIFY_PROJECT_SCHEDULE_ACTIVITIES_DELETED,),
    Events.PROJECT_SCHEDULE_ACTIVITIES_ASSIGNED: (Actions.NOTIFY_PROJECT_SCHEDULE_ACTIVITIES_ASSIGNED,),
    Events.PROJECT_SCHEDULE_ACTIVITY_EDITED: (Actions.WEBHOOK_PROJECT_SCHEDULE_ACTIVITIES_EDITED,),
    Events.PROJECT_SCHEDULE_ACTIVITY_CREATED: (Actions.WEBHOOK_PROJECT_SCHEDULE_ACTIVITIES_CREATED,),
    Events.PROJECT_SCHEDULE_ACTIVITY_UPDATED: (Actions.WEBHOOK_PROJECT_SCHEDULE_ACTIVITIES_UPDATED,),
    Events.PROJECT_SCHEDULE_ACTIVITY_DELETED: (Actions.WEBHOOK_PROJECT_SCHEDULE_ACTIVITIES_DELETED,),
    Events.FACEBOOK_LEAD_CREATED: (Actions.WEBHOOK_FACEBOOK_LEAD_CREATED,),
    Events.LEAD_CREATED: (Actions.WEBHOOK_LEAD_CREATED,),
    Events.PROJECT_SYNC_SHEET_CREATED: (Actions.WEBHOOK_PROJECT_SYNC_SHEET_CREATED,),
}
