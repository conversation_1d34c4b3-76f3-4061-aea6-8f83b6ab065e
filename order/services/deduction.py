import decimal
from typing import List

from django.utils import timezone

from common.services import model_update, nested_object_segregation
from core.caches import OrganizationCountryConfigCache
from core.selectors import get_tax_slab_list
from order.data.models import Deduction, DeductionAttachment
from order.domain.entities.domain_entities import DeductionAttachmentData, DeductionData
from order.domain.entities.entities import (
    VendorOrderDeductionValidationInputEntity,
)


class DeductionPersistanceService:
    @classmethod
    def get_codes(cls, project_id: int, limit: int = 1) -> List[int]:
        deduction = (
            Deduction.all_objects.filter(project_id=project_id).order_by("-code").values_list("code", flat=True).first()
        )
        if deduction:
            return [int(deduction) + i for i in range(1, limit + 1)]
        return list(range(1, limit + 1))

    @classmethod
    def create(
        cls, project_id: int, order_id: int, deduction_data: DeductionData, user_id: int, save: bool = True
    ) -> Deduction:
        deduction = Deduction()
        deduction.name = deduction_data.name
        deduction.amount = deduction_data.amount
        deduction.type = deduction_data.type
        deduction.remark = deduction_data.remark
        deduction.type_color_code = deduction_data.type_color_code
        deduction.item_reference = deduction_data.item_reference
        deduction.tax_amount = deduction_data.tax_amount
        deduction.order_id = order_id
        deduction.project_id = project_id
        if not getattr(deduction_data, "code"):
            deduction.code = cls.get_codes(project_id)[0]
        else:
            deduction.code = deduction_data.code
        deduction.created_by_id = user_id
        if save:
            deduction.full_clean()
            deduction.save()
        return deduction

    @classmethod
    def update(
        cls, deduction_instance: Deduction, deduction_data: DeductionData, user_id: int, save: bool = True
    ) -> Deduction:
        deduction, _, _ = model_update(
            instance=deduction_instance,
            data=deduction_data,
            save=save,
            fields=["name", "amount", "type", "remark", "item_reference", "type_color_code", "tax_amount"],
            updated_by_id=user_id,
        )
        return deduction

    @classmethod
    def bulk_create(
        cls, project_id: int, order_id: int, deduction_entities: List[DeductionData], user_id: int
    ) -> List[Deduction]:
        deduction_objs = []
        codes = cls.get_codes(project_id=project_id, limit=len(deduction_entities))
        for deduction_data, code in zip(deduction_entities, codes):
            setattr(deduction_data, "code", code)
            deduction_objs.append(
                cls.create(
                    project_id=project_id, order_id=order_id, deduction_data=deduction_data, user_id=user_id, save=False
                )
            )
        return Deduction.objects.bulk_create(objs=deduction_objs)

    @classmethod
    def bulk_update(cls, deduction_entities: List[DeductionData], user_id: int):
        deduction_instances: List[Deduction] = Deduction.objects.filter(
            id__in=[deduction_data.id for deduction_data in deduction_entities]
        ).all()
        deduction_objs = []
        for deduction_instance, deduction_data in zip(deduction_instances, deduction_entities):
            deduction_objs.append(
                cls.update(
                    deduction_instance=deduction_instance, deduction_data=deduction_data, user_id=user_id, save=False
                )
            )
        Deduction.objects.bulk_update(
            objs=deduction_objs,
            fields=["name", "amount", "type", "remark", "item_reference", "updated_by_id", "updated_at", "tax_amount"],
        )

    @classmethod
    def bulk_delete(cls, deduction_ids: List[int], user_id: int):
        Deduction.objects.filter(id__in=deduction_ids).update(deleted_by_id=user_id, deleted_at=timezone.now())

    @classmethod
    def validate_deduction(cls, vo_deduction_input_entity: VendorOrderDeductionValidationInputEntity, org_id: int):
        org_config = OrganizationCountryConfigCache.get(instance_id=org_id)
        max_tax_slab_percent = org_config.tax_type.max_slab_percent
        order_base_amount = vo_deduction_input_entity.order.base_amount
        deduction_base_amount = vo_deduction_input_entity.deduction.base_amount
        order_tax_amount = vo_deduction_input_entity.order.tax_amount
        deduction_tax_amount = vo_deduction_input_entity.deduction.tax_amount
        final_order_base_amount = order_base_amount - deduction_base_amount
        final_order_tax_amount = order_tax_amount - deduction_tax_amount
        if final_order_tax_amount > (final_order_base_amount * decimal.Decimal(max_tax_slab_percent) / 100):
            raise ValueError(f"Tax amount must not be greater than {max_tax_slab_percent}% of total amount.")


class DeductionAttachmentPersistanceService:
    @classmethod
    def create(
        cls,
        deduction_attachment_data: DeductionAttachmentData,
        user_id: int,
        save: bool = True,
    ) -> DeductionAttachment:
        deduction_attachment = DeductionAttachment()
        deduction_attachment.name = deduction_attachment_data.name
        deduction_attachment.file = deduction_attachment_data.file
        deduction_attachment.deduction_id = deduction_attachment_data.deduction_id
        deduction_attachment.uploaded_by_id = user_id
        if save:
            deduction_attachment.full_clean()
            deduction_attachment.save()
        return deduction_attachment

    @classmethod
    def bulk_create(
        cls,
        attachment_entities: List[DeductionAttachmentData],
        user_id: int,
    ) -> List[DeductionAttachment]:
        deduction_attachment_objs = (
            cls.create(deduction_attachment_data=attachment, user_id=user_id, save=False)
            for attachment in attachment_entities
        )
        return DeductionAttachment.objects.bulk_create(objs=deduction_attachment_objs)

    @classmethod
    def bulk_delete(cls, deduction_attachment_ids: List[int], user_id: int):
        DeductionAttachment.objects.filter(id__in=deduction_attachment_ids).update(
            deleted_by_id=user_id, deleted_at=timezone.now()
        )


class DeductionAndAttachmentService:
    @classmethod
    def process_create(
        cls, project_id: int, order_id: int, deduction_entities: List[DeductionData], user_id: int
    ) -> Deduction:
        deductions: List[Deduction] = DeductionPersistanceService.bulk_create(
            project_id=project_id, order_id=order_id, deduction_entities=deduction_entities, user_id=user_id
        )
        if deductions:
            attachment_entities = []
            for deduction_entity, deduction in zip(deduction_entities, deductions):
                for attachment in deduction_entity.attachments:
                    setattr(attachment, "deduction_id", deduction.pk)
                    attachment_entities.append(attachment)
            DeductionAttachmentPersistanceService.bulk_create(attachment_entities=attachment_entities, user_id=user_id)
        return deductions

    @classmethod
    def process_update(cls, deduction_entities: List[DeductionData], user_id: int) -> Deduction:
        DeductionPersistanceService.bulk_update(deduction_entities=deduction_entities, user_id=user_id)
        to_create_attachments = []
        to_delete_attachments = []
        for deduction_entity in deduction_entities:
            to_create, _, to_delete = nested_object_segregation(docs_list=deduction_entity.attachments)
            for attachment in to_create:
                setattr(attachment, "deduction_id", deduction_entity.id)
                to_create_attachments.append(attachment)
            to_delete_attachments.extend(to_delete)
        if to_create_attachments:
            DeductionAttachmentPersistanceService.bulk_create(
                attachment_entities=to_create_attachments,
                user_id=user_id,
            )
        if to_delete_attachments:
            DeductionAttachmentPersistanceService.bulk_delete(
                deduction_attachment_ids=[attachment.id for attachment in to_delete_attachments], user_id=user_id
            )

    @classmethod
    def process_delete(cls, deduction_entities: List[DeductionData], user_id: int):
        DeductionPersistanceService.bulk_delete(deduction_ids=[data.id for data in deduction_entities], user_id=user_id)
