from django.db.models import TextChoices
from django.utils.translation import gettext_lazy as _

from common.choices import SectionKey


class Actions(TextChoices):
    ## Webhook (TASK)
    WEBHOOK_TASK_DONE = "webhook_task_done", _("Webhook Task Done")  # ???
    WEBHOOK_TASK_CREATED = "webhook_task_created", _("Webhook Task Created")

    WEBHOOK_APPROVAL_REQUEST_CREATED = "webhook_approval_request_created", _("Webhook Approval Request Created")
    WEBHOOK_APPROVAL_REQUEST_APPROVED = (
        "webhook_approval_request_approved",
        _("Webhook Approval Request Approved"),
    )
    WEBHOOK_APPROVAL_REQUEST_EDITED = "webhook_approval_request_edited", _("Webhook Approval Request Edited")

    ## Webhook (USER)
    WEBHOOK_USER_CREATED = "webhook_user_created", _("Webhook User Created")
    WEBHOOK_USER_LOGGED_IN = "webhook_user_logged_in", _("Webhook User Logged In")

    ## Webhook (ORDER)
    WEBHOOK_ORDER_CREATED = "webhook_order_created", _("Webhook Order Created")
    WEBHOOK_ORDER_SENT = "webhook_order_sent", _("Webhook Order Sent")
    WEBHOOK_ORDER_SENT_WITHOUT_NOTIFICATION = (
        "webhook_order_sent_without_notification",
        _("Webhook Order Sent Without Notification"),
    )
    WEBHOOK_ORDER_MODIFIED = "webhook_order_modified", _("Webhook Order Modified")
    WEBHOOK_DRAFT_ORDER_MODIFIED = "webhook_draft_order_modified", _("Webhook Draft Order Modified")
    WEBHOOK_ORDER_CANCELLED = "webhook_order_cancelled", _("Webhook Order Cancelled")

    WEBHOOK_PO_REVISED = "webhook_po_revised", _("Webhook PO Revised")
    WEBHOOK_PO_UPLOADED = "webhook_po_uploaded", _("Webhook PO Uploaded")

    ## Webhook (ORGANIZATION)
    WEBHOOK_ORGANIZATION_CREATED = "webhook_organization_created", _("Webhook Organization Created")

    ## Webhook (PROGRESS_REPORT)
    WEBHOOK_DPR_CREATED = "webhook_dpr_created", _("Webhook DPR Created")
    WEBHOOK_BOQ_ELEMENT_PROGRESS_PERCENTAGE_UPDATE = (
        "webhook_boq_element_progress_percentage_update",
        _("Webhook BOQ Element Progress Percentage Update"),
    )

    ## Webhook (DESIGN_FILE)
    WEBHOOK_DESIGN_FILE_UPLOAD = "webhook_design_file_upload", _("Webhook Design File Upload")
    WEBHOOK_DESIGN_NEW_VERSION_UPLOADED = (
        "webhook_design_new_version_uploaded",
        _("Webhook Design New Version Uploaded"),
    )
    WEBHOOK_DESIGN_FILE_APPROVED = "webhook_design_file_approved", _("Webhook Design File Approved")
    WEBHOOK_DESIGN_FREEZE = "webhook_design_freeze", _("Webhook Design Freeze")
    WEBHOOK_DESIGN_FILE_STATUS_UPDATED = "webhook_design_file_status_updated", _("Webhook Design File Status Updated")

    ## WEBHOOK (SNAG)
    WEBHOOK_SNAG_POC_ALLOTED = "webhook_snag_poc_alloted", _("Webhook Snag POC Alloted")
    WEBHOOK_SNAG_ASSIGNED = "webhook_snag_assigned", _("Webhook Snag Assigned")

    ## WEBHOOK (PROPOSAL)
    WEBHOOK_PROPOSAL_APPROVED = "webhook_proposal_approved", _("Webhook Proposal Approved")
    WEBHOOK_PROPOSAL_REJECTED = "webhook_proposal_rejected", _("Webhook Proposal Rejected")
    WEBHOOK_PROPOSAL_CREATED = "webhook_proposal_created", _("Webhook Proposal Created")
    WEBHOOK_PROPOSAL_UPDATED_WHEN_ORDER_EXIST = (
        "webhook_proposal_updated_when_order_exist",
        _("Webhook Proposal Updated When Order Exist"),
    )
    WEBHOOK_PROPOSAL_UPDATED_WHEN_ORDER_NOT_EXIST = (
        "webhook_proposal_updated_when_order_not_exist",
        _("Webhook Proposal Updated When Order Not Exist"),
    )

    ## WEBHOOK (PAYMENT_REQUEST)
    WEBHOOK_PAYMENT_ENTRY_CREATED = "webhook_proposal_entry_created", _("Webhook Payment Entry Created")

    ## WEBHOOK (LEAD)
    WEBHOOK_LEAD_QUOTATION_SUBMITTED = "webhook_lead_quotation_submitted", _("Webhook Lead Quotation Submitted")
    WEBHOOK_LEAD_ASSIGNMENT = "webhook_lead_assignment", _("Webhook Lead Assignment")
    WEBHOOK_LEAD_CREATED = "webhook_lead_created", _("Webhook Lead Created")
    WEBHOOK_LEAD_STAGE_CHANGED = "webhook_lead_stage_changed", _("Webhook Lead Stage Changed")

    ## WEBHOOK (PROJECT)
    WEBHOOK_PROJECT_CREATED = "webhook_project_created", _("Webhook Project Created")
    WEBHOOK_MARK_EXECUTION_COMPLETED = "webhook_mark_execution_completed", _("Webhook Mark Execution Completed")
    WEBHOOK_PROJECT_ASSIGNMENT = "webhook_project_assignment", _("Webhook Project Assignment")

    ## WEBHOOK (RECCE)
    WEBHOOK_SINGLE_RECCE_APPROVED = "webhook_single_recce_approved", _("Webhook Single Recce Approved")
    WEBHOOK_RECCE_SUBMITTED = "webhook_rece_submitted", _("Webhook Recce Submitted")

    ## WEBHOOK (PROJECT_SCHEDULE)
    WEBHOOK_PROJECT_SCHEDULE_ACTIVITIES_CREATED = (
        "webhook_project_schedule_activities_created",
        _("Webhook Project Schedule Activities Created"),
    )
    WEBHOOK_PROJECT_SCHEDULE_ACTIVITIES_UPDATED = (
        "webhook_project_schedule_activities_updated",
        _("Webhook Project Schedule Activities Updated"),
    )
    WEBHOOK_PROJECT_SCHEDULE_ACTIVITIES_DELETED = (
        "webhook_project_schedule_activities_deleted",
        _("Webhook Project Schedule Activities Deleted"),
    )
    WEBHOOK_PROJECT_SCHEDULE_ACTIVITIES_EDITED = (
        "webhook_project_schedule_activities_edited",
        _("Webhook Project Schedule Activities Edited"),
    )
    WEBHOOK_FACEBOOK_LEAD_CREATED = (
        "webhook_facebook_lead_created",
        _("Webhook Facebook Lead Created"),
    )
    WEBHOOK_NEW_INVOICE_UPLOADED = "webhook_new_invoice_uploaded", _("Webhook New Invoice Uploaded")
    WEBHOOK_PROJECT_SYNC_SHEET_CREATED = "webhook_project_sync_sheet_created", _("Webhook Project Sync Sheet Created")

    # COmments
    NOTIFY_PROJECT_COMMENT = "notify_project_comment", _("Notify Project Comment")
    NOTIFY_COMMENT_MENTIONED = "notify_comment_mentioned"
    NOTIFY_COMMENT_APPROVAL_REQUESTED = "notify_comment_approval_requested"
    NOTIFY_COMMENT_APPROVAL_ACCEPTED = "notify_comment_approval_accepted"
    NOTIFY_COMMENT_APPROVAL_REJECTED = "notify_comment_approval_rejected"
    NOTIFY_PROJECT_COMMENT_MENTIONED = "notify_project_comment_mentioned"
    NOTIFY_PROJECT_COMMENT_APPROVAL_ACCEPTED = "notify_project_comment_approval_accepted"
    NOTIFY_PROJECT_COMMENT_APPROVAL_REJECTED = "notify_project_comment_approval_rejected"
    NOTIFY_PROJECT_COMMENT_APPROVAL_REQUESTED = "notify_project_comment_approval_requested"
    NOTIFY_COMMENT_APPROVAL_MENTIONED = "notify_comment_approval_mentioned"
    NOTIFY_COMMENT_APPROVAL_ACCEPTED_REJECTED = "notify_comment_approval_accepted_rejected"

    # Recce
    NOTIFY_RECCE_LINK_CREATION = "notify_recce_link_creation", _("Notify Recce Link creation")
    NOTIFY_RECCE_STARTED = "notify_recce_started", _("Notify Recce Started")
    NOTIFY_RECCE_SUBMISSION = "notify_recce_submission", _("Notify Recce Submission")
    NOTIFY_RECCE_SUBMISSION_SELF = "notify_recce_submission_self", _("Notify Self Recce Submission")
    NOTIFY_RECCE_ASSIGNED = "notify_recce_assigned", _("Notify Recce Assigned")
    NOTIFY_RECCE_FEEDBACK_FORM = "notify_recce_feedback_form", _("Notify Recce Feedback Form")
    NOTIFY_RECCE_UPDATED = "notify_recce_updated", _("Notify Recce Updated")
    NOTIFY_RECCE_APPROVED = "notify_recce_approved", _("Notify Recce Approved")

    # Design
    NOTIFY_DESIGN_FREEZE = "notify_design_freeze", _("Notify Design Freeze")
    NOTIFY_DESIGN_APPROVED = "notify_design_approved", _("Notify Design Approved")
    NOTIFY_DESIGN_NEW_VERSION_UPLOADED = "notify_design_new_version_uploaded", _("Notify Design New Version Uploaded")
    NOTIFY_DESIGN_NEW_VERSION_POST_FREEZE_UPLOADED = (
        "notify_design_new_version_post_freeze_uploaded",
        _("Notify Design New Version Post Freeze Uploaded"),
    )

    # Project
    NOTIFY_PROJECT_CREATION = "notify_project_creation", _("Notify Project Creation")
    NOTIFY_PROJECT_ASSIGNMENT = "notify_project_assignment", _("Notify Project Assignment")
    NOTIFY_PROJECT_SHARED = "notify_project_shared", _("Notify Project Shared")
    NOTIFY_PROJECT_USER_REMOVED = "notify_project_user_removed", _("Notify Project User Removed")
    NOTIFY_PROJECT_DATE_CHANGED = "notify_project_date_changed", _("Notify Project Date Changed")
    NOTIFY_PROJECT_DATE_ASSIGN = "notify_project_date_assign", _("Notify Project Date Assign")

    NOTIFY_NEW_INVOICE_UPLOADED = "notify_new_invoice_uploaded", _("Notify New Invoice Uploaded")
    NOTIFY_ALL_INVOICES_MARKED_UPLOADED = (
        "notify_all_invoices_marked_uploaded",
        _("Notify All Invoices Marked Uploaded"),
    )
    NOTIFY_WORK_PROGRESS_REPORT_PDF_GENERATED = (
        "notify_work_progress_report_pdf_generated",
        _("Notify Work Progress Report PDF Generated"),
    )
    NOTIFY_WORK_PROGRESS_SUBSCRIBERS_REPORT_PDF_GENERATED = (
        "notify_work_progress_subscribers_report_pdf_generated",
        _("Notify Work Progress Subscribers Report PDF Generated"),
    )
    NOTIFY_WORK_PROGRESS_EXPORT_REPORT_PDF_GENERATED = (
        "notify_work_progress_export_report_pdf_generated",
        _("Notify Work Progress Export Report PDF Generated"),
    )
    NOTIFY_MARK_EXECUTION_COMPLETED = "notify_mark_execution_completed", _("Notify Mark Execution Completed")
    NOTIFY_CLIENT_FOR_WORK_PROGRESS_REPORT_PDF_GENERATED = (
        "notify_client_for_work_progress_report_pdf_generated",
        _("Notify Client For Work Progress Report PDF Generated"),
    )
    NOTIFY_CLIENT_SUBSCRIBERS_FOR_WORK_PROGRESS_REPORT_PDF_GENERATED = (
        "notify_client_subscribers_for_work_progress_report_pdf_generated",
        _("Notify Client Subscribers For Work Progress Report PDF Generated"),
    )

    # Approval Request
    NOTIFY_APPROVAL_REQUEST_CREATED = "notify_approval_request_created", _("Notify Approval Request Created")
    NOTIFY_APPROVAL_REQUEST_APPROVED = "notify_approval_request_approved", _("Notify Approval Request Approved")
    NOTIFY_APPROVAL_REQUEST_FINALLY_APPROVED = (
        "notify_approval_request_finally_approved",
        _("Notify Approval Request Finally Approved"),
    )
    NOTIFY_APPROVAL_REQUEST_REJECTED = (
        "notify_approval_request_rejected",
        _("Notify Approval Request Rejected"),
    )
    NOTIFY_APPROVAL_REQUEST_EDITED = (
        "notify_approval_request_edited",
        _("Notify Approval Request Edited"),
    )
    NOTIFY_APPROVAL_REQUEST_HOLD = (
        "notify_approval_request_hold",
        _("Notify Approval Request Hold"),
    )
    RESOURCE_REQUEST_CANCEL = "resource_request_cancel", _("Resource Request Cancel")
    CALLBACK_APPROVAL_REQUEST_CREATED = "callback_approval_request_created", _("Callback Approval Request Created")
    CALLBACK_APPROVAL_REQUEST_APPROVED = (
        "callback_approval_request_approved",
        _("Callback Approval Request Approved"),
    )
    CALLBACK_APPROVAL_REQUEST_FINALIZED = (
        "callback_approval_request_finalized",
        _("Callback Approval Request Finalized"),
    )

    # Snag
    NOTIFY_SNAG_ASSIGNMENT_TO_CREATOR = "notify_snag_assignment_to_creator", _("Notify Snag Assignment To Creator")
    NOTIFY_SNAG_ASSIGNMENT_TO_ASSIGNEE = "notify_snag_assignment_to_assignee", _("Notify Snag Assignment To Assignee")
    NOTIFY_SNAG_BULK_ASSIGNMENT_TO_CREATORS = (
        "notify_snag_bulk_assignment_to_creators",
        _("Notify Snag Bulk Assignment To Creators"),
    )
    NOTIFY_SNAG_BULK_ASSIGNMENT_TO_ASSIGNEE = (
        "notify_snag_bulk_assignment_to_assignee",
        _("Notify Snag Bulk Assignment To Assignee"),
    )
    NOTIFY_SNAG_ALLOT_AND_COMMIT_TIMELINE_TO_ALLOTEE = "notify_snag_allot_and_commit_timeline_to_allotee"
    NOTIFY_SNAG_ALLOT_AND_COMMIT_TIMELINE_TO_OBSERVERS = "notify_snag_allot_and_commit_timeline"
    NOTIFY_SNAG_UNRESOLVED = "notify_snag_unresolved"
    NOTIFY_SNAG_BULK_ALLOT_AND_TIMELINE_COMMIT_TO_ALLOTEE = "notify_snag_bulk_allot_and_commit_timeline_to_allotee"
    NOTIFY_SNAG_BULK_ALLOT_AND_COMMIT_TIMELINE_TO_OBSERVERS = "notify_snag_bulk_allot_and_commit_timeline_to_observers"

    NOTIFY_USER_ONBOARDED = "notify_user_onboarded", _("Notify User Onboarded")
    CALLBACK_TASK_CREATED = "callback_task_created", _("Callback Task Created")
    CALLBACK_USER_CREATED = "callback_user_created", _("Callback User Created")
    CALLBACK_DPR_CREATED = "callback_dpr_created", _("Callback DPR Created")
    CALLBACK_PROJECT_CREATED = "callback_project_created", _("Callback Project Created")
    CALLBACK_SINGLE_RECCE_APPROVED = "callback_single_recce_approved", _("Callback Single Recce Approved")
    CALLBACK_RECCE_SUBMITTED = "callback_rece_submitted", _("Callback Recce Submitted")

    CALLBACK_TASK_DONE = "callback_task_done", _("Callback Task Done")
    CALLBACK_DESIGN_FILE_UPLOAD = "callback_design_file_upload", _("Callback Design File Upload")
    CALLBACK_SNAG_POC_ALLOTED = "callback_snag_poc_alloted", _("Callback Snag POC Alloted")

    SCHEDULE_MISCONFIG_REQUEST = "schedule_misconfig_request", _("Schedule Misconfig Request")
    SKIP_REQUESTS_MISCONFIGURED_LEVELS = "skip_requests_misconfigured_levels", _("Skip Requests Misconfigured Levels")
    CALLBACK_SNAG_ASSIGNED = "callback_snag_assigned", _("Callback Snag Assigned")
    DESIGN_FILE_APPROVED = "design_file_approved", _("Design File Approved")
    CALLBACK_DESIGN_NEW_VERSION_UPLOADED = (
        "callback_design_new_version_uploaded",
        _("Callback Design New Version Uploaded"),
    )
    CALLBACK_BOQ_ELEMENT_PROGRESS_PERCENTAGE_UPDATE = (
        "callback_boq_element_progress_percentage_update",
        _("Callback BOQ Element Progress Percentage Update"),
    )

    CALLBACK_ORGANIZATION_CREATED = (
        "callback_organization_created",
        _("Callback Organization Created"),
    )
    CALLBACK_APPROVAL_REQUEST_EDITED = "callback_approval_request_edited", _("Callback Approval Request Edited")

    # Material
    CALLBACK_STOCK_TRANSFER_BATCH_APPROVED = (
        "callback_stock_transfer_batch_approved",
        _("Callback Stock Transfer Batch Approved"),
    )
    NOTIFY_STOCK_TRANSFER_BATCH_CREATED = (
        "notify_stock_transfer_batch_created",
        _("Notify Stock Transfer Batch Created"),
    )
    NOTIFY_STOCK_TRANSFER_BATCH_ACTION = (
        "notify_stock_transfer_batch_action",
        _("Notify Stock Transfer Batch Action "),
    )

    # Proposal
    CALLBACK_PROPOSAL_APPROVED = "callback_proposal_approved", _("Callback Proposal Approved")
    CALLBACK_PROPOSAL_REJECTED = "callback_proposal_rejected", _("Callback Proposal Rejected")
    CALLBACK_PROPOSAL_CREATED = "callback_proposal_created", _("Callback Proposal Created")
    NOTIFY_PROPOSAL_REQUEST_FOR_NEW_ORDER = (
        "notify_proposal_request_for_new_order",
        _("Notify Proposal Request For New Order"),
    )
    NOTIFY_PROPOSAL_REQUEST_FOR_ORDER_CHANGE = (
        "notify_proposal_request_for_order_change",
        _("Notify Proposal Request For Order Change"),
    )
    NOTIFY_PROPOSAL_APPROVE_FOR_NEW_ORDER = (
        "notify_proposal_approve_for_new_order",
        _("Notify Proposal Approve For New Order"),
    )
    NOTIFY_PROPOSAL_APPROVE_FOR_ORDER_CHANGE = (
        "notify_proposal_approve_for_order_change",
        _("Notify Proposal Approve For Order Change"),
    )
    NOTIFY_PROPOSAL_REJECT_FOR_NEW_ORDER = (
        "notify_proposal_reject_for_new_order",
        _("Notify Proposal Reject For New Order"),
    )
    NOTIFY_PROPOSAL_REJECT_FOR_ORDER_CHANGE = (
        "notify_proposal_reject_for_order_change",
        _("Notify Proposal Reject For Order Change"),
    )
    NOTIFY_PROPOSAL_SENT = "notify_proposal_sent", _("Notify Proposal Sent")
    NOTIFY_PROPOSAL_REJECTED = "notify_proposal_rejected", _("Notify Proposal Rejected")

    # PO
    CALLBACK_PO_UPLOADED = "callback_po_uploaded", _("Callback PO Uploaded")
    CALLBACK_PO_REVISED = "callback_po_revised", _("Callback PO Revised")
    NOTIFY_PO_SENT = "notify_po_sent", _("Notify PO Sent")
    NOTIFY_PO_RECEIVED = "notify_po_received", _("Notify PO Received")
    NOTIFY_PO_SENT_CANCELLED = "notify_po_sent_cancelled", _("Notify PO Sent Cancelled")
    NOTIFY_PO_UPLOADED_EMAIL = "notify_po_uploaded_email", _("Notify PO Uploaded Email")
    NOTIFY_PO_CANCELLED_EMAIL = "notify_po_cancelled_email", _("Notify PO Cancelled Email")
    NOTIFY_PO_REVISED_EMAIL = "notify_po_revised_email", _("Notify PO Revised Email")
    NOTIFY_PO_RECEIVED_CANCELLED = "notify_po_received_cancelled", _("Notify PO Received Cancelled")

    # Order
    CALLBACK_ORDER_MODIFIED = "callback_order_modified", _("Callback Order Modified")
    CALLBACK_ORDER_CREATED = "callback_order_created", _("Callback Order Created")
    CALLBACK_ORDER_SENT = "callback_order_sent", _("Callback Order Sent")
    CALLBACK_ORDER_CANCELLED = "callback_order_cancelled", _("Callback Order Cancelled")
    NOTIFY_ORDER_RECEIEVED = "notify_order_received", _("Notify Order Received")
    NOTIFY_ORDER_SENT_COMPLETED = "notify_order_sent_completed", _("Notify Order Sent Completed")
    NOTIFY_ORDER_RECEIEVED_COMPLETED = "notify_order_received_completed", _("Notify Order Received Completed")
    NOTIFY_ORDER_SENT = "notify_order_sent", _("Notify Order Sent")
    NOTIFY_ORDER_SENT_MODIFIED = "notify_order_sent_modified", _("Notify Order Sent Modified")
    NOTIFY_ORDER_RECEIVED_MODIFIED = "notify_order_received_modified", _("Notify Order Received Modified")
    NOTIFY_ORDER_SENT_CANCELLED = "notify_order_sent_cancelled", _("Notify Order Sent Cancelled")
    NOTIFY_ORDER_RECEIVED_CANCELLED = "notify_order_received_cancelled", _("Notify Order Received Cancelled")
    NOTIFY_ORDER_SENT_WITHOUT_PROPOSAL = "notify_order_sent_without_proposal", _("Notify Order Sent Without Proposal")
    NOTIFY_ORDER_MODIFY_WITHOUT_PROPOSAL = (
        "notify_order_modify_without_proposal",
        _("Notify Order Modify Without Proposal"),
    )
    NOTIFY_ORDER_CANCEL_WITHOUT_PROPOSAL = (
        "notify_order_cancel_without_proposal",
        _("Notify Order Cancel Without Proposal"),
    )

    CALLBACK_PAYMENT_ENTRY_CREATED = "callback_proposal_entry_created", _("Callback Payment Entry Created")
    NOTIFY_TASK_ASSIGNED = "notify_task_assigned", _("Notify Task Assigned")
    NOTIFY_TASK_MENTIONED = "notify_task_mentioned", _("Notify Task Mentioned")
    NOTIFY_TASK_REPLY_MENTIONED = "notify_task_reply_mentioned", _("Notify Task Reply Mentioned")
    NOTIFY_APPROVAL_REQUEST_REPLY_MENTIONED = (
        "notify_approval_request_reply_mentioned",
        _("Notify Approval Request Reply Mentioned"),
    )
    NOTIFY_TASK_UPDATED = "notify_task_updated", _("Notify Task Updated")
    NOTIFY_TASK_DONE = "notify_task_done", _("Notify Task Done")
    NOTIFY_TASK_ARCHIVED_FOR_ALL = "notify_task_archived_for_all", _("Notify Task Archived For All")
    NOTIFY_TASK_REMINDER = "notify_task_reminder", _("Notify Task Reminder")
    NOTIFY_TODAYS_DUE_TASK_COUNT = "notify_todays_due_task_count", _("Notify Todays Due Task Count")

    # Leads
    NOTIFY_LEAD_ASSIGNMENT = "notify_lead_assignment", _("Notify Lead Assignment")
    NOTIFY_BOARD_ASSIGNMENT = "notify_board_assignment", _("Notify Board Assignment")
    CALLBACK_LEAD_QUOTATION_SUBMITTED = "callback_lead_quotation_submitted", _("Callback Lead Quotation Submitted")

    # JSW Integration
    CALLBACK_UPDATE_POC_ROLE = "callback_update_poc_role", _("Callback Update POC Role")

    # Project Schedule
    NOTIFY_PROJECT_SCHEDULE_COMPLETED = (
        "notify_project_schedule_completed",
        _("Notify Project Schedule Completed"),
    )
    NOTIFY_PROJECT_SCHEDULE_ACTIVITIES_ASSIGNED = (
        "notify_project_schedule_activities_assigned",
        _("Notify Project Schedule Activities Assigned"),
    )
    NOTIFY_PROJECT_SCHEDULE_ACTIVITIES_DELETED = (
        "notify_project_schedule_activities_deleted",
        _("Notify Project Schedule Activities Deleted"),
    )
    NOTIFY_PROJECT_SCHEDULE_DELAY = (
        "notify_project_schedule_delay",
        _("Notify Project Schedule Delay"),
    )
    NOTIFY_PROJECT_SCHEDULE_OVERDUE = (
        "notify_project_schedule_overdue",
        _("Notify Project Schedule Overdue"),
    )

    # Work Progress
    TEMPLATE_WORK_PROGRESS_GENERATE_REPORT = (
        "template_work_progress_generate_report",
        _("Template Work Progress Generate Report"),
    )
    TEMPLATE_WORK_PROGRESS_EXPORT_REPORT = (
        "template_work_progress_export_report",
        _("Template Work Progress Export Report"),
    )
    LEAD_EXCEL_EXPORT = (
        "lead_excel_export",
        _("Lead Excel Export"),
    )


class Permissions(TextChoices):
    # Normal Core Permission
    CAN_VIEW_RATE_CONTRACT = "can_view_rate_contract", _("Can View Rate Contract")
    CAN_ACCESS_ELEMENT_LIBRARY = "can_view_element_library", _("Can View Element Library")
    CAN_ADD_STORE_TYPE = "can_add_store_type", _("Can Add Store Type")
    CAN_ACCESS_MY_ELEMENT_LIBRARY = "can_view_my_element_library", _("Can View My Element Library")
    CAN_ACCESS_SHARED_ELEMENT_LIBRARY = "can_view_shared_element_library", _("Can View Shared Element Library")
    CAN_CREATE_ELEMENT_LIBRARY = "can_create_element_library", _("Can Create Element Library")
    CAN_EDIT_ELEMENT_LIBRARY_GLOBAL_SCOPE = (
        "can_edit_element_library_global_scope",
        _("Can Edit Element Library Global Scope"),
    )
    CAN_EDIT_ELEMENT_LIBRARY_LOCAL_SCOPE = (
        "can_edit_element_library_local_scope",
        _("Can Edit Element Library Local Scope"),
    )
    CAN_ACCESS_GOOGLE_SHEET = "can_access_google_sheet", _("Can Access Google Sheet")
    CAN_CREATE_PROJECT = "can_create_project", _("Can Create Project")
    CAN_VIEW_CLIENT_RATE = "can_view_client_rate", _("Can View Client Rate")
    CAN_VIEW_ORDER_RATE = "can_view_order_rate", _("Can View Order Rate")
    CAN_VIEW_SCHEDULE_SHEET = "can_view_schedule_sheet", _("Can View Schedule Sheet")
    CAN_ACCESS_FINANCE = "can_access_finance", _("Can Access Finance")
    CAN_ACCESS_COST_COMPARISON = "can_access_cost_compare", _("Can Access Cost Comparison")
    CAN_ACCESS_RUNNING_SPENDS = "can_access_running_spends", _("Can Access Running Spends")
    CAN_SHARE_PROJECT = "can_share_project", _("Can Share Project")
    CAN_ACCESS_REQUESTS = "can_access_requests", _("Can Access Requests")
    CAN_ACCESS_APPROVALS = "can_access_approvals", _("Can Access Approvals")
    CAN_CREATE_CLIENT = "can_create_client", _("Can Create Client")

    CAN_CAPTURE_FILE_IN_DPR = "can_capture_file_in_dpr", _("Can Capture File In DPR")
    CAN_ACCESS_MANAGE_VENDOR = "can_access_manage_vendor", _("Can Access Manage Vendor")
    CAN_ACCESS_MATERIALS = "can_access_materials", _("Can Access Materials")
    CAN_CREATE_GRN = (
        "can_create_grn",
        _("Can Create GRN"),
    )
    CAN_UPDATE_STOCK_CONSUMPTION = "can_update_stock_consumption", _("Can Update Stock Consumption")
    CAN_CANCEL_STOCK_TRANSFER = "can_cancel_stock_transfer", _("Can Cancel Stock Transfer")
    CAN_APPROVE_STOCK_TRANSFER = "can_approve_stock_transfer", _("Can Approve Stock Transfer")
    CAN_EDIT_MANAGE_VENDOR = "can_edit_manage_vendor", _("Can Edit Manage Vendor")
    CAN_CHANGE_VENDOR_STATUS = "can_change_vendor_status", _("Can Change Vendor Status")
    CAN_ACCESS_METABASE_DASHBOARD = "can_access_metabase_dashboard", _("Can Access Metabase Dashboard")
    CAN_ACCESS_MANAGE_CLIENT = "can_access_manage_client", _("Can Access Manage Client")
    CAN_ACCESS_WORK_PROGRESS_MATERIALS = "can_access_work_progress_materials", _("Can Access Work Progress Materials")
    CAN_EDIT_CLIENT = "can_edit_client", _("Can Edit Client")
    CAN_EDIT_APPROVAL_HIERARCHY = "can_edit_approval_hierarchy", _("Can Edit Approval Hierarchy")
    CAN_VIEW_BASE_AMOUNT = "can_view_base_amount", _("Can View Base Amount")
    CAN_VIEW_SERVICE_CHARGE = "can_view_service_charge", _("Can View Service Charge")
    CAN_EDIT_SERVICE_CHARGE = "can_edit_service_charge", _("Can Edit Service Charge")
    CAN_VIEW_DISCOUNT = "can_view_discount", _("Can View Discount")
    CAN_EDIT_DISCOUNT = "can_edit_discount", _("Can Edit Discount")
    CAN_ACCESS_BOARDS = "can_access_boards", _("Can Access Boards")
    CAN_VIEW_ALL_BOARDS = "can_view_all_boards", _("Can View All Boards")
    CAN_EDIT_BOARD = "can_edit_board", _("Can Edit Board")
    CAN_VIEW_LEAD_CONTACTS = "can_view_lead_contacts", _("Can View Lead Contacts")
    CAN_VIEW_LEAD_COMPANIES = "can_view_lead_companies", _("Can View Lead Companies")
    CAN_ACCESS_INSIGHTS = "can_access_insights", _("Can Access Insights")
    CAN_CREATE_DASHBOARD_COLLECTIONS = "can_create_dashboard_collections", _("Can Create Dashboard Collection")
    CAN_VIEW_BUDGET_RATE = "can_view_budget_rate", _("Can View Budget Rate")

    # Direct Core Permission
    CAN_EDIT_ORG_USER = "can_edit_org_user", _("Can Edit Org User")
    CAN_EDIT_PROJECT_ROLE = "can_edit_project_role", _("Can Edit Project Role")
    CAN_VIEW_ALL_PROJECTS = "can_view_all_projects", _("Can View All Projects")
    CAN_RECEIVE_PROJECT_SHARE_NOTIFICATIONS = (
        "can_receive_project_share_notifications",
        _("Can Receive Project Share Notifications"),
    )
    CAN_ACCESS_ORG_SETTINGS = "can_access_org_settings", _("Can Access Org Settings")
    CAN_ACCESS_MODULE_CONFIGURATIONS = "can_access_module_configurations", _("Can Access Module Configurations")
    CAN_EDIT_RECCE_TEMPLATE = "can_edit_recce_template", _("Can Edit Recce Template")
    CAN_ACCESS_OTHER_EXPENSE = "can_access_other_expense", _("Can Access Other Expense")
    CAN_VIEW_ALL_EXPENSE_REQUEST = "can_view_all_expense_request", _("Can View All Expense Request")
    CAN_VIEW_ALL_TASKS = "can_view_all_tasks", _("Can View All Tasks")

    CAN_VIEW_SCOPE_PROGRESS_ON_PROJECT_LIST = (
        "can_view_scope_progress_on_project_list",
        _("Can View Scope Progress On Project List"),
    )
    CAN_VIEW_SCHEDULE_PROGRESS_ON_PROJECT_LIST = (
        "can_view_schedule_progress_on_project_list",
        _("Can View Schedule Progress On Project List"),
    )
    CAN_ASSIGN_USER_ON_MULTIPLE_PROJECT_USER_ROLE = (
        "can_assign_user_on_multiple_project_user_role",
        _("Can Assign User On Multiple Project User Role"),
    )

    # Non Admin Core Permission
    FOR_TESTING = "for_testing", _("For Testing")
    CAN_ACCESS_VMS = "can_access_vms", _("Can Access VMS")
    CAN_EDIT_VMS = "can_edit_vms", _("Can Edit VMS")
    # Admin and core permission
    CAN_APPROVE_ALL_APPROVAL_REQUESTS = ("can_approve_all_approval_requests", _("Can Approve All Approval Requests"))
    CAN_CANCEL_ALL_APPROVAL_REQUESTS = ("can_cancel_all_approval_requests", _("Can Cancel All Approval Requests"))

    # Normal Project Permission
    CAN_CREATE_RECCE_LINK = "can_create_recce_link", _("Can Create Recce Link")

    # Design
    CAN_VIEW_DESIGN_MODULE = "can_view_design_module", _("Can View Design Module")

    CAN_VIEW_DESIGN_FILES = "can_view_design_files", _("Can View Design Files")  # deprecated need to remove this
    CAN_VIEW_DRAFT_DESIGN_FILES = "can_view_draft_design_files", _("Can View Draft Design Files")
    CAN_VIEW_REJECTED_DESIGN_FILES = "can_view_rejected_design_files", _("Can View Rejected Design Files")
    CAN_VIEW_REVIEWED_DESIGN_FILES = "can_view_reviewed_design_files", _("Can View Reviewed Design Files")
    CAN_VIEW_APPROVED_DESIGN_FILES = "can_view_approved_design_files", _("Can View Approved Design Files")
    CAN_VIEW_CLIENT_REJECTED_FILES = "can_view_client_rejected_files", _("Can View Client Rejected Files")
    CAN_EDIT_DESIGN_FILES = "can_edit_design_files", _("Can Edit Design Files")
    CAN_FREEZE_DESIGN = "can_freeze_design", _("Can Freeze Design")
    CAN_DOWNLOAD_DESIGN_FILES = "can_download_design_files", _("Can Download Design Files")
    CAN_DELETE_DESIGN_FILES = "can_delete_design_files", _("Can Delete Design Files")
    CAN_APPROVE_DESIGN_FILES = "can_approve_design_files", _("Can Approve Design Files")
    CAN_REJECT_DESIGN_FILES = "can_reject_design_files", _("Can Reject Design Files")
    CAN_MARK_REVIEWED_DESIGN_FILES = "can_mark_reviewed_design_files", _("Can Mark Reviewed Design Files")
    CAN_MARK_CLIENT_REJECT_DESIGN_FILES = "can_mark_client_rejected_files", _("Can Mark Client Rejected Files")
    CAN_ACCESS_PROJECT_ATTACHMENT = "can_access_project_attachment", _("Can Access Project All Details")
    CAN_ACCESS_PROJECT_CUSTOM_FIELDS = "can_access_project_custom_fields", _("Can Access Project Custom Fields")
    CAN_EDIT_PROJECT = "can_edit_project", _("Can Edit Project")
    CAN_ACCESS_RECCE = "can_access_recce", _("Can Access Recce")
    CAN_EDIT_RECCE_DATA = "can_edit_recce_data", _("Can Edit Recce Data")
    CAN_UPLOAD_RECCE_FILES = "can_upload_recce_files", _("Can Upload Recce Files")
    CAN_APPROVE_RECCE = "can_approve_recce", _("Can Approve Recce")

    # Direct Project Permission
    CAN_ACCESS_BOQ = "can_access_boq", _("Can Access Boq")
    CAN_EDIT_BOQ = "can_edit_boq", _("Can Edit Boq")
    CAN_ASSIGN_PROJECT_USER = "can_assign_project_user", _("Can Assign Project User")
    CAN_CREATE_OUTGOING_ORDER = "can_create_order", _("Can Create Outgoing Order")
    CAN_SEND_OUTGOING_ORDER = "can_send_order", _("Can Send Outgoing Order")
    CAN_CANCEL_OUTGOING_ORDER = "can_cancel_order", _("Can Cancel Outgoing Order")
    CAN_DELETE_OUTGOING_ORDER = "can_delete_order", _("Can Delete Outgoing Order")
    CAN_RECEIVE_PROJECT_NOTIFICATIONS = "can_receive_project_notifications", _("Can Receive Project Notifications")
    CAN_CANCEL_INCOMING_ORDER = "can_cancel_incoming_order", _("Can Cancel Incoming Order")  # deprecated
    CAN_EDIT_PROPOSAL = "can_edit_proposal", _("Can Edit Proposal")
    CAN_VIEW_PROPOSAL_MAIL_BOX = "can_view_proposal_mail_box", _("Can View Proposal Mail Box")
    CAN_SEND_PROPOSAL = "can_send_proposal", _("Can Send Proposal")
    CAN_APPROVE_PROPOSAL = "can_approve_proposal", _("Can Approve Proposal")
    CAN_RECEIVE_PROJECT_COMMENT_NOTIFICATIONS = (
        "can_receive_project_comment_notifications",
        _("Can Receive Project Comment Notifications"),
    )
    CAN_ACCESS_CONSOLIDATED_PROGRESS_REPORT = (
        "can_access_consolidated_progress_report",
        _("Can Access Consolidated Progress Report"),
    )
    CAN_ACCESS_EXPORT_BOQ_PDF = "can_access_export_boq_pdf", _("Can Access Export Boq PDF")
    CAN_ACCESS_EXPORT_AND_SHARE_BOQ_PDF = (
        "can_access_export_and_share_boq_pdf",
        _("Can Access Export And Share Boq PDF"),
    )
    CAN_ACCESS_EXPORT_SNAG_PDF = "can_access_export_snag_pdf", _("Can Access Export Snag PDF")
    CAN_ACCESS_EXPORT_GRN_PDF = "can_access_export_grn_pdf", _("Can Access Export GRN PDF")
    CAN_ACCESS_MY_SCOPE = "can_access_my_scope", _("Can Access My Scope")
    CAN_ACCESS_INCOMING_ORDER = "can_access_incoming_order", _("Can Access Incoming Order")
    CAN_ACCESS_PROPOSAL_FOR_CLIENT = "can_access_proposal_for_client", _("Can Access Proposal For Client")
    CAN_ACCESS_OUTGOING_ORDER = "can_access_outgoing_order", _("Can Access Outgoing Order")
    CAN_ACCESS_PROPOSAL_FROM_VENDOR = "can_access_proposal_from_vendor", _("Can Access Proposal From Vendor")
    CAN_ACCESS_VENDOR_WISE_SCOPE = "can_access_vendor_wise_scope", _("Can Access Vendor Wise Scope")
    CAN_ACCESS_VENDOR_WISE_PROGRESS = "can_access_vendor_wise_progress", _("Can Access Vendor Wise Progress")
    CAN_RECEIVE_RECCE_CREATION_NOTIFICATIONS = (
        "can_receive_recce_creation_notifications",
        _("Can Receive Recce Creation Notifications"),
    )
    CAN_RECEIVE_RECCE_START_NOTIFICATIONS = (
        "can_receive_recce_start_notifications",
        _("Can Receive Recce Start Notifications"),
    )
    CAN_RECEIVE_RECCE_SUBMISSION_NOTIFICATIONS = (
        "can_receive_recce_submission_notifications",
        _("Can Receive Recce Submission Notifications"),
    )
    CAN_RECEIVE_RECCE_UPDATE_NOTIFICATIONS = (
        "can_receive_recce_update_notifications",
        _("Can Receive Recce Update Notifications"),
    )
    CAN_RECEIVE_RECCE_APPROVE_NOTIFICATIONS = (
        "can_receive_recce_approve_notifications",
        _("Can Receive Recce Approve Notifications"),
    )
    CAN_RECEIVE_DESIGN_APPROVE_NOTIFICATIONS = (
        "can_receive_design_approve_notifications",
        _("Can Receive Design Approve Notifications"),
    )
    CAN_RECEIVE_DESIGN_FREEZE_NOTIFICATIONS = (
        "can_receive_design_freeze_notifications",
        _("Can Receive Design Freeze Notifications"),
    )
    CAN_RECEIVE_INCOMING_ORDER_RECEIVED_NOTIFICATIONS = (
        "can_receive_incoming_order_received_notifications",
        _("Can Receive Incoming Order Received Notifications"),
    )
    CAN_RECEIVE_INCOMING_ORDER_MODIFIED_NOTIFICATIONS = (
        "can_receive_incoming_order_modified_notifications",
        _("Can Receive Incoming Order Modified Notifications"),
    )
    CAN_RECEIVE_INCOMING_ORDER_COMPLETED_NOTIFICATIONS = (
        "can_receive_incoming_order_completed_notifications",
        _("Can Receive Incoming Order Completed Notifications"),
    )
    CAN_RECEIVE_INCOMING_ORDER_PO_RECEIVED_NOTIFICATIONS = (
        "can_receive_incoming_order_po_received_notifications",
        _("Can Receive Incoming Order PO Received Notifications"),
    )
    CAN_RECEIVE_INCOMING_ORDER_PO_CANCELLED_NOTIFICATIONS = (
        "can_receive_incoming_order_po_cancelled_notifications",
        _("Can Receive Incoming Order PO Cancelled Notifications"),
    )
    CAN_RECEIVE_OUTGOING_ORDER_SENT_NOTIFICATIONS = (
        "can_receive_outgoing_order_sent_notifications",
        _("Can Receive Outgoing Order Sent Notifications"),
    )
    CAN_RECEIVE_OUTGOING_ORDER_COMPLETED_NOTIFICATIONS = (
        "can_receive_outgoing_order_completed_notifications",
        _("Can Receive Outgoing Order Completed Notifications"),
    )
    CAN_RECEIVE_OUTGOING_ORDER_PO_SENT_NOTIFICATIONS = (
        "can_receive_outgoing_order_po_sent_notifications",
        _("Can Receive Outgoing Order PO Sent Notifications"),
    )
    CAN_RECEIVE_OUTGOING_ORDER_PO_CANCELLED_NOTIFICATIONS = (
        "can_receive_outgoing_order_po_cancelled_notifications",
        _("Can Receive Outgoing Order PO Cancelled Notifications"),
    )
    CAN_RECEIVE_OUTGOING_ORDER_MODIFIED_NOTIFICATIONS = (
        "can_receive_outgoing_order_modified_notifications",
        _("Can Receive Outgoing Order Modified Notifications"),
    )
    CAN_RECEIVE_OUTGOING_ORDER_NEW_INVOICE_UPLOADED_NOTIFICATIONS = (
        "can_receive_outgoing_order_new_invoice_uploaded_notifications",
        _("Can Receive Outgoing Order New Invoice Uploaded Notifications"),
    )
    CAN_RECEIVE_OUTGOING_ORDER_ALL_INVOICE_MARKED_UPLOADED_NOTIFICATIONS = (
        "can_receive_outgoing_order_all_invoice_marked_uploaded_notifications",
        _("Can Receive Outgoing Order All Invoice Marked Uploaded Notifications"),
    )
    CAN_ACCESS_SNAG = "can_access_snag", _("Can Access Snag")
    CAN_LINK_SNAG_ITEMS = "can_link_snag_items", _("Can Link Snag Items")
    CAN_RECEIVE_PROGRESS_REPORT_NOTIFICATIONS = (
        "can_receive_progress_report_notifications",
        _("Can Receive Progress Report Notifications"),
    )
    CAN_RECEIVE_PROPOSAL_SENT_NOTIFICATIONS = (
        "can_receive_proposal_from_vendor_notifications",
        _("Can Receive Proposal from Vendor Notifications"),
    )
    CAN_RECEIVE_PROPOSAL_REJECTED_NOTIFICATIONS = (
        "can_receive_proposal_rejected_notifications",
        _("Can Receive Proposal REJECTED Notifications"),
    )
    CAN_RECEIVE_DESIGN_NEW_VERSION_NOTIFICATION = (
        "can_receive_design_new_version_notification",
        _("Can Receive Design New Version Notification"),
    )
    CAN_RECEIVE_DESIGN_NEW_VERSION_POST_FREEZE_UPLOADED_NOTIFICATION = (
        "can_receive_design_new_version_post_freeze_notification",
        _("Can Receive Design New Version Post Freeze Notification"),
    )
    CAN_RECEIVE_PROJECT_SCHEDULE_COMPLETED_NOTIFICATIONS = (
        "can_receive_project_schedule_completed_notification",
        _("Can Receive Project Schedule Completed Notification"),
    )
    CAN_RECEIVE_PROJECT_SCHEDULE_DELAYED_NOTIFICATIONS = (
        "can_receive_project_schedule_delayed_notification",
        _("Can Receive Project Schedule Delayed Notification"),
    )
    CAN_RECEIVE_PROJECT_SCHEDULE_OVERDUE_NOTIFICATIONS = (
        "can_receive_project_schedule_overdue_notification",
        _("Can Receive Project Schedule Overdue Notification"),
    )
    CAN_RECEIVE_PROJECT_SCHEDULE_ACTIVITIES_DELETED_NOTIFICATIONS = (
        "can_receive_project_schedule_activities_deleted_notification",
        _("Can Receive Project Schedule Activities Deleted Notification"),
    )
    CAN_RECEIVE_PROJECT_SCHEDULE_ACTIVITIES_ASSIGNED_NOTIFICATIONS = (
        "can_receive_project_schedule_activities_assigned_notification",
        _("Can Receive Project Schedule Activities Assigned Notification"),
    )
    CAN_PREVIEW_PURCHASE_ORDER = "can_preview_purchase_order", _("Can Preview Purchase Order")
    CAN_UPDATE_PROJECT_STATUS = "can_update_project_status", _("Can Update Project Hold/Lost Status")

    # Project Level Project AND Core Permission
    CAN_ADD_CUSTOM_BOQ_ELEMENT = "can_add_custom_boq_element", _("Can Add Custom BOQ Element")
    CAN_IMPORT_BOQ_ELEMENT_FROM_PROJECT = (
        "can_import_boq_element_from_project",
        _("Can Import BOQ Element From Project"),
    )
    CAN_IMPORT_BOQ_ELEMENT_FROM_LIBRARY = (
        "can_import_boq_element_from_library",
        _("Can Import BOQ Element From Library"),
    )
    CAN_ACCESS_ORDER = "can_access_order", _("Can Access Order")
    CAN_CREATE_INCOMING_ORDER = "can_create_incoming_order", _("Can Create Incoming Order")  # deprecated
    CAN_CREATE_PROPOSAL = "can_create_proposal", _("Can Create Proposal")
    CAN_CLOSE_ORDER = "can_close_order", _("Can Close Order")
    CAN_UPLOAD_BOQ_ELEMENT_USING_EXCEL = "can_upload_boq_element_using_excel", _("Can Upload BOQ Element Using Excel")
    CAN_APPROVE_CLIENT_PROPOSAL = "can_approve_client_proposal", _("Can Approve Client Proposal")
    CAN_RESTRICT_CLIENT_EDIT_PROPOSAL = "can_restrict_client_edit_proposal", _("Can Restrict Client Edit Proposal")
    CAN_CLOSE_SNAG = "can_close_snag", _("Can Close Snag")
    CAN_CANCEL_PO = "can_cancel_po", _("Can Cancel PO")
    CAN_ACCESS_PROJECT_ACTIVITY_SCHEDULE = (
        "can_access_project_activity_schedule",
        _("Can Access Project Activity Schedule"),
    )
    CAN_UPDATE_PROJECT_ACTIVITY_SCHEDULE = (
        "can_update_project_activity_schedule",
        _("Can Update Project Activity Schedule"),
    )  # only update actual dates and completion %
    CAN_EDIT_PROJECT_ACTIVITY_SCHEDULE = (
        "can_edit_project_activity_schedule",
        _("Can Edit Project Activity Schedule"),
    )  # for create, edit, update, delete
    CAN_ACCESS_VENDOR_INVOICES = "can_access_vendor_invoices", _("Can Access Vendor Invoices")

    # Project Level Project OR Core Permission
    CAN_RECEIVE_OUTGOING_ORDER_CANCEL_NOTIFICATIONS = (
        "can_receive_order_cancel_notifications",
        _("Can Receive Outgoing Order Cancel Notifications"),
    )
    CAN_RECEIVE_INCOMING_ORDER_CANCEL_NOTIFICATIONS = (
        "can_receive_incoming_order_cancel_notifications",
        _("Can Receive Incoming Order Cancel Notifications"),
    )
    CAN_ACCESS_CLIENT_PAYMENT_REQUEST = "can_access_client_payment_request", _("Can Access Client Payment Request")
    CAN_ACCESS_VENDOR_PAYMENT_REQUEST = "can_access_vendor_payment_request", _("Can Access Vendor Payment Request")
    CAN_CREATE_VENDOR_PAYMENT_REQUEST = "can_create_vendor_payment_request", _("Can Create Vendor Payment Request")
    CAN_CREATE_PAYMENT_ENTRY = "can_create_payment_entry", _("Can Create Payment Entry")
    CAN_CREATE_EXPENSE_PAYMENT_ENTRY = "can_create_expense_payment_entry", _("Can Create Expense Payment Entry")
    CAN_ARCHIVE_PROJECTS = "can_archive_projects", _("Can Archive Projects")
    CAN_MARK_EXPENSE_CLOSURE = "can_mark_expense_closure", _("Can Mark Expense Closure")

    CAN_RECEIVE_SNAG_NOTIFICATIONS = (
        "can_receive_snag_notifications",
        _("Can Receive Snag Notifications"),
    )  # used for both project user role and org user role
    CAN_CONFIGURE_PAYMENT_TERMS_FOR_VENDOR_ORDER = (
        "can_configure_payment_terms_for_vendor_order",
        _("Can Configure Payment Terms For Vendor Order"),
    )
    CAN_CONFIGURE_OTHER_TERMS_AND_CONDITIONS_FOR_VENDOR_ORDER = (
        "can_configure_other_terms_and_conditions_for_vendor_order",
        _("Can Configure Other Terms And Conditions For Vendor Order"),
    )
    CAN_CONFIGURE_TERMS_AND_CONDITIONS_FOR_QUOTATION = (
        "can_configure_term_and_conditions_for_quotation",
        _("Can Configure Terms And Conditions For Quotation"),
    )
    CAN_CONFIGURE_CLIENT_VIEW = (
        "can_configure_client_view",
        _("Can Configure Client View"),
    )
    CAN_INTEGRATE_CRM_WITH_META = "can_integrate_crm_with_meta", _("Can Integrate CRM With Meta")
    CAN_COMPARE_BOQ_VS_ORDER = "can_compare_boq_vs_order", _("Can Compare BOQ Vs Order")

    # Work Progress
    CAN_ACCESS_WORK_PROGRESS = "can_access_work_progress", _("Can Access Work Progress")
    CAN_UPDATE_WORK_PROGRESS = "can_update_work_progress", _("Can Update Work Progress")
    CAN_GENERATE_PROGRESS_REPORT = "can_generate_progress_report", _("Can Generate Progress Report")
    CAN_EDIT_GENERATE_REPORT_SETTINGS = "can_edit_generate_report_settings", _("Can Edit Generate Report Settings")
    CAN_EDIT_EXPORT_REPORT_SETTINGS = "can_edit_export_report_settings", _("Can Edit Export Report Settings")
    CAN_DELETE_PROGRESS_REPORT = "can_delete_progress_report", _("Can Delete Progress Report")
    CAN_EXPORT_PROGRESS_REPORT = "can_export_progress_report", _("Can Export Progress Report")
    CAN_DELETE_FILE_IN_DPR = "can_delete_file_in_dpr", _("Can Delete File In DPR")
    CAN_MARK_AND_UNMARK_EXECUTION_COMPLETE = (
        "can_mark_and_unmark_execution_complete",
        _("Can Mark And Unmark Execution Complete"),
    )
    CAN_ACCESS_MY_REPORTS = "can_access_my_reports", _("Can Access My Reports")
    CAN_CHANGE_PROGRESS_UPDATE_METHOD = "can_change_progress_update_method", _("Can Change Progress Update Method")
    CAN_MANAGE_DPR_SUBSCRIPTION = "can_manage_dpr_subscription", _("Can Manage DPR Subscription")
    CAN_UPLOAD_GALLERY_FILE_IN_DPR = "can_upload_gallery_file_in_dpr", _("Can Upload Gallery File In DPR")
    CAN_EDIT_TDS_AMOUNT = "can_edit_tds_amount", _("Can Edit TDS Amount")


PROJECT_PERMISSION_SECTION_MAPPINGS = {
    Permissions.CAN_ACCESS_BOQ.value: SectionKey.MY_SCOPE,
    Permissions.CAN_EDIT_BOQ.value: SectionKey.MY_SCOPE,
    Permissions.CAN_CREATE_RECCE_LINK.value: SectionKey.RECCE,
    Permissions.CAN_VIEW_DESIGN_FILES.value: SectionKey.DESIGN,
    Permissions.CAN_EDIT_DESIGN_FILES.value: SectionKey.DESIGN,
    Permissions.CAN_DOWNLOAD_DESIGN_FILES.value: SectionKey.DESIGN,
    Permissions.CAN_DELETE_DESIGN_FILES.value: SectionKey.DESIGN,
    Permissions.CAN_ASSIGN_PROJECT_USER.value: SectionKey.PROJECT,
    Permissions.CAN_CREATE_OUTGOING_ORDER.value: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_SEND_OUTGOING_ORDER.value: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_CANCEL_OUTGOING_ORDER.value: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_DELETE_OUTGOING_ORDER.value: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_ACCESS_OTHER_EXPENSE.value: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_FREEZE_DESIGN.value: SectionKey.DESIGN,
    Permissions.CAN_VIEW_CLIENT_RATE.value: SectionKey.PROJECT,
    Permissions.CAN_VIEW_ORDER_RATE.value: SectionKey.PROJECT,
    Permissions.CAN_ADD_CUSTOM_BOQ_ELEMENT.value: SectionKey.BOQ,
    Permissions.CAN_IMPORT_BOQ_ELEMENT_FROM_PROJECT.value: SectionKey.BOQ,
    Permissions.CAN_IMPORT_BOQ_ELEMENT_FROM_LIBRARY.value: SectionKey.BOQ,
    Permissions.CAN_ACCESS_ORDER.value: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_RECEIVE_PROJECT_NOTIFICATIONS: SectionKey.PROJECT,
    Permissions.CAN_ASSIGN_USER_ON_MULTIPLE_PROJECT_USER_ROLE: SectionKey.PROJECT,
    Permissions.CAN_ACCESS_PROJECT_ATTACHMENT: SectionKey.PROJECT,
    Permissions.CAN_ACCESS_PROJECT_CUSTOM_FIELDS: SectionKey.PROJECT,
    Permissions.CAN_CREATE_INCOMING_ORDER: SectionKey.BOQ,
    Permissions.CAN_CANCEL_INCOMING_ORDER: SectionKey.BOQ,
    Permissions.CAN_CREATE_PROPOSAL: SectionKey.BOQ,
    Permissions.CAN_EDIT_PROPOSAL: SectionKey.BOQ,
    Permissions.CAN_VIEW_PROPOSAL_MAIL_BOX: SectionKey.BOQ,
    Permissions.CAN_SEND_PROPOSAL: SectionKey.BOQ,
    Permissions.CAN_APPROVE_PROPOSAL: SectionKey.BOQ,
    Permissions.CAN_RECEIVE_PROJECT_COMMENT_NOTIFICATIONS: SectionKey.PROJECT,
    Permissions.CAN_EDIT_PROJECT: SectionKey.PROJECT,
    Permissions.CAN_ACCESS_RECCE: SectionKey.RECCE,
    Permissions.CAN_EDIT_RECCE_DATA: SectionKey.RECCE,
    Permissions.CAN_APPROVE_DESIGN_FILES: SectionKey.DESIGN,
    Permissions.CAN_REJECT_DESIGN_FILES: SectionKey.DESIGN,
    Permissions.CAN_MARK_REVIEWED_DESIGN_FILES: SectionKey.DESIGN,
    Permissions.CAN_MARK_CLIENT_REJECT_DESIGN_FILES: SectionKey.DESIGN,
    Permissions.CAN_VIEW_APPROVED_DESIGN_FILES: SectionKey.DESIGN,
    Permissions.CAN_VIEW_CLIENT_REJECTED_FILES: SectionKey.DESIGN,
    Permissions.CAN_VIEW_REJECTED_DESIGN_FILES: SectionKey.DESIGN,
    Permissions.CAN_VIEW_REVIEWED_DESIGN_FILES: SectionKey.DESIGN,
    Permissions.CAN_VIEW_DRAFT_DESIGN_FILES: SectionKey.DESIGN,
    Permissions.CAN_UPLOAD_RECCE_FILES: SectionKey.RECCE,
    Permissions.CAN_APPROVE_RECCE: SectionKey.RECCE,
    Permissions.CAN_ACCESS_MY_SCOPE: SectionKey.BOQ,
    Permissions.CAN_ACCESS_INCOMING_ORDER: SectionKey.BOQ,
    Permissions.CAN_ACCESS_PROPOSAL_FOR_CLIENT: SectionKey.BOQ,
    Permissions.CAN_ACCESS_OUTGOING_ORDER: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_ACCESS_PROPOSAL_FROM_VENDOR: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_ACCESS_VENDOR_WISE_SCOPE: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_ACCESS_VENDOR_WISE_PROGRESS: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_ACCESS_WORK_PROGRESS: SectionKey.WORK_REPORTS,
    Permissions.CAN_UPDATE_WORK_PROGRESS: SectionKey.WORK_REPORTS,
    Permissions.CAN_RECEIVE_RECCE_CREATION_NOTIFICATIONS: SectionKey.RECCE,
    Permissions.CAN_RECEIVE_RECCE_START_NOTIFICATIONS: SectionKey.RECCE,
    Permissions.CAN_RECEIVE_RECCE_SUBMISSION_NOTIFICATIONS: SectionKey.RECCE,
    Permissions.CAN_RECEIVE_RECCE_UPDATE_NOTIFICATIONS: SectionKey.RECCE,
    Permissions.CAN_RECEIVE_RECCE_APPROVE_NOTIFICATIONS: SectionKey.RECCE,
    Permissions.CAN_RECEIVE_DESIGN_APPROVE_NOTIFICATIONS: SectionKey.DESIGN,
    Permissions.CAN_RECEIVE_DESIGN_FREEZE_NOTIFICATIONS: SectionKey.DESIGN,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_RECEIVED_NOTIFICATIONS: SectionKey.INCOMING_ORDER,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_MODIFIED_NOTIFICATIONS: SectionKey.INCOMING_ORDER,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_COMPLETED_NOTIFICATIONS: SectionKey.INCOMING_ORDER,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_PO_RECEIVED_NOTIFICATIONS: SectionKey.INCOMING_ORDER,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_PO_CANCELLED_NOTIFICATIONS: SectionKey.INCOMING_ORDER,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_CANCEL_NOTIFICATIONS: SectionKey.INCOMING_ORDER,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_SENT_NOTIFICATIONS: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_COMPLETED_NOTIFICATIONS: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_PO_SENT_NOTIFICATIONS: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_PO_CANCELLED_NOTIFICATIONS: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_MODIFIED_NOTIFICATIONS: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_NEW_INVOICE_UPLOADED_NOTIFICATIONS: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_ALL_INVOICE_MARKED_UPLOADED_NOTIFICATIONS: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_CANCEL_NOTIFICATIONS: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_RECEIVE_PROGRESS_REPORT_NOTIFICATIONS: SectionKey.PROGRESS_UPDATE,
    Permissions.CAN_CLOSE_ORDER: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_ACCESS_SNAG: SectionKey.SNAG,
    Permissions.CAN_LINK_SNAG_ITEMS: SectionKey.SNAG,
    Permissions.CAN_UPLOAD_BOQ_ELEMENT_USING_EXCEL: SectionKey.BOQ,
    Permissions.CAN_RECEIVE_PROPOSAL_SENT_NOTIFICATIONS: SectionKey.PROPOSAL_FROM_VENDOR,
    Permissions.CAN_RECEIVE_PROPOSAL_REJECTED_NOTIFICATIONS: SectionKey.PROPOSAL_FOR_CLIENT,
    Permissions.CAN_RECEIVE_DESIGN_NEW_VERSION_NOTIFICATION: SectionKey.DESIGN,
    Permissions.CAN_RECEIVE_DESIGN_NEW_VERSION_POST_FREEZE_UPLOADED_NOTIFICATION: SectionKey.DESIGN,
    Permissions.CAN_UPDATE_PROJECT_STATUS: SectionKey.PROJECT,
    Permissions.CAN_APPROVE_CLIENT_PROPOSAL: SectionKey.PROPOSAL_FOR_CLIENT,
    Permissions.CAN_CLOSE_SNAG: SectionKey.SNAG,
    Permissions.CAN_CANCEL_PO: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_ACCESS_MATERIALS: SectionKey.INVENTORY,
    Permissions.CAN_CREATE_GRN: SectionKey.INVENTORY,
    Permissions.CAN_UPDATE_STOCK_CONSUMPTION: SectionKey.INVENTORY,
    Permissions.CAN_CANCEL_STOCK_TRANSFER: SectionKey.INVENTORY,
    Permissions.CAN_APPROVE_STOCK_TRANSFER: SectionKey.INVENTORY,
    Permissions.CAN_CHANGE_PROGRESS_UPDATE_METHOD: SectionKey.PROGRESS_UPDATE,
    Permissions.CAN_VIEW_BUDGET_RATE: SectionKey.PROJECT,
    Permissions.CAN_RECEIVE_SNAG_NOTIFICATIONS: SectionKey.SNAG,
    Permissions.CAN_VIEW_DESIGN_MODULE: SectionKey.DESIGN,
    Permissions.CAN_ACCESS_PROJECT_ACTIVITY_SCHEDULE: SectionKey.PROJECT_ACTIVITY_SCHEDULE,
    Permissions.CAN_UPDATE_PROJECT_ACTIVITY_SCHEDULE: SectionKey.PROJECT_ACTIVITY_SCHEDULE,
    Permissions.CAN_EDIT_PROJECT_ACTIVITY_SCHEDULE: SectionKey.PROJECT_ACTIVITY_SCHEDULE,
    Permissions.CAN_RECEIVE_PROJECT_SCHEDULE_COMPLETED_NOTIFICATIONS: SectionKey.PROJECT_ACTIVITY_SCHEDULE,
    Permissions.CAN_RECEIVE_PROJECT_SCHEDULE_DELAYED_NOTIFICATIONS: SectionKey.PROJECT_ACTIVITY_SCHEDULE,
    Permissions.CAN_RECEIVE_PROJECT_SCHEDULE_OVERDUE_NOTIFICATIONS: SectionKey.PROJECT_ACTIVITY_SCHEDULE,
    Permissions.CAN_RECEIVE_PROJECT_SCHEDULE_ACTIVITIES_DELETED_NOTIFICATIONS: SectionKey.PROJECT_ACTIVITY_SCHEDULE,
    Permissions.CAN_RECEIVE_PROJECT_SCHEDULE_ACTIVITIES_ASSIGNED_NOTIFICATIONS: SectionKey.PROJECT_ACTIVITY_SCHEDULE,
    Permissions.CAN_ACCESS_VENDOR_INVOICES: SectionKey.VENDOR_INVOICES,
    Permissions.CAN_GENERATE_PROGRESS_REPORT: SectionKey.WORK_REPORTS,
    Permissions.CAN_EDIT_GENERATE_REPORT_SETTINGS: SectionKey.WORK_REPORTS,
    Permissions.CAN_EDIT_EXPORT_REPORT_SETTINGS: SectionKey.WORK_REPORTS,
    Permissions.CAN_DELETE_PROGRESS_REPORT: SectionKey.WORK_REPORTS,
    Permissions.CAN_EXPORT_PROGRESS_REPORT: SectionKey.WORK_REPORTS,
    Permissions.CAN_DELETE_FILE_IN_DPR: SectionKey.WORK_REPORTS,
    Permissions.CAN_CAPTURE_FILE_IN_DPR: SectionKey.WORK_REPORTS,
    Permissions.CAN_MARK_AND_UNMARK_EXECUTION_COMPLETE: SectionKey.WORK_REPORTS,
    Permissions.CAN_UPLOAD_GALLERY_FILE_IN_DPR: SectionKey.WORK_REPORTS,
    Permissions.CAN_MANAGE_DPR_SUBSCRIPTION: SectionKey.WORK_REPORTS,
    Permissions.CAN_ACCESS_VENDOR_PAYMENT_REQUEST: SectionKey.VENDOR_PAYMENTS,
    Permissions.CAN_CREATE_VENDOR_PAYMENT_REQUEST: SectionKey.VENDOR_PAYMENTS,
    Permissions.CAN_CREATE_PAYMENT_ENTRY: SectionKey.VENDOR_PAYMENTS,
}


CORE_PERMISSION_SECTION_MAPPINGS = {
    Permissions.CAN_VIEW_RATE_CONTRACT.value: SectionKey.ELEMENT_LIBRARY,
    Permissions.CAN_VIEW_CLIENT_RATE.value: SectionKey.ELEMENT_LIBRARY,
    Permissions.CAN_VIEW_ORDER_RATE.value: SectionKey.PROJECT,
    Permissions.CAN_ADD_STORE_TYPE.value: SectionKey.ELEMENT_LIBRARY,
    Permissions.CAN_ACCESS_ELEMENT_LIBRARY.value: SectionKey.ELEMENT_LIBRARY,
    Permissions.CAN_ACCESS_MY_ELEMENT_LIBRARY.value: SectionKey.ELEMENT_LIBRARY,
    Permissions.CAN_ACCESS_SHARED_ELEMENT_LIBRARY.value: SectionKey.ELEMENT_LIBRARY,
    Permissions.CAN_CREATE_ELEMENT_LIBRARY.value: SectionKey.ELEMENT_LIBRARY,
    Permissions.CAN_EDIT_ELEMENT_LIBRARY_GLOBAL_SCOPE.value: SectionKey.ELEMENT_LIBRARY,
    Permissions.CAN_EDIT_ELEMENT_LIBRARY_LOCAL_SCOPE.value: SectionKey.ELEMENT_LIBRARY,
    Permissions.CAN_ACCESS_GOOGLE_SHEET.value: SectionKey.OTHERS,
    Permissions.CAN_CREATE_PROJECT: SectionKey.PROJECT,
    Permissions.CAN_ACCESS_VMS.value: SectionKey.VMS,
    Permissions.CAN_EDIT_VMS.value: SectionKey.VMS,
    Permissions.FOR_TESTING: SectionKey.OTHERS,
    Permissions.CAN_APPROVE_ALL_APPROVAL_REQUESTS: SectionKey.OTHERS,  # TODO : change section
    Permissions.CAN_CANCEL_ALL_APPROVAL_REQUESTS: SectionKey.OTHERS,  # TODO : change section
    Permissions.CAN_VIEW_SCHEDULE_SHEET: SectionKey.OTHERS,
    Permissions.CAN_ACCESS_FINANCE: SectionKey.FINANCE,
    Permissions.CAN_ACCESS_COST_COMPARISON: SectionKey.FINANCE,
    Permissions.CAN_ACCESS_RUNNING_SPENDS: SectionKey.FINANCE,
    Permissions.CAN_SHARE_PROJECT: SectionKey.PROJECT,
    Permissions.CAN_EDIT_ORG_USER: SectionKey.ORG_SETTINGS,
    Permissions.CAN_EDIT_PROJECT_ROLE: SectionKey.OTHERS,
    Permissions.CAN_ACCESS_ORDER: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_CREATE_PROPOSAL: SectionKey.BOQ,
    Permissions.CAN_CREATE_INCOMING_ORDER: SectionKey.BOQ,
    Permissions.CAN_ADD_CUSTOM_BOQ_ELEMENT: SectionKey.BOQ,
    Permissions.CAN_IMPORT_BOQ_ELEMENT_FROM_PROJECT: SectionKey.BOQ,
    Permissions.CAN_IMPORT_BOQ_ELEMENT_FROM_LIBRARY: SectionKey.BOQ,
    Permissions.CAN_CLOSE_ORDER: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_RECEIVED_NOTIFICATIONS: SectionKey.INCOMING_ORDER,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_MODIFIED_NOTIFICATIONS: SectionKey.INCOMING_ORDER,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_COMPLETED_NOTIFICATIONS: SectionKey.INCOMING_ORDER,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_PO_RECEIVED_NOTIFICATIONS: SectionKey.INCOMING_ORDER,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_PO_CANCELLED_NOTIFICATIONS: SectionKey.INCOMING_ORDER,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_CANCEL_NOTIFICATIONS: SectionKey.INCOMING_ORDER,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_SENT_NOTIFICATIONS: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_COMPLETED_NOTIFICATIONS: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_PO_SENT_NOTIFICATIONS: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_PO_CANCELLED_NOTIFICATIONS: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_MODIFIED_NOTIFICATIONS: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_NEW_INVOICE_UPLOADED_NOTIFICATIONS: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_ALL_INVOICE_MARKED_UPLOADED_NOTIFICATIONS: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_CANCEL_NOTIFICATIONS: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_CREATE_CLIENT: SectionKey.ELEMENT_LIBRARY,
    Permissions.CAN_ACCESS_REQUESTS: SectionKey.OTHERS,
    Permissions.CAN_ACCESS_APPROVALS: SectionKey.OTHERS,
    Permissions.CAN_VIEW_ALL_PROJECTS: SectionKey.PROJECT,
    Permissions.CAN_RECEIVE_PROJECT_SHARE_NOTIFICATIONS: SectionKey.PROJECT,
    Permissions.CAN_ACCESS_BOQ.value: SectionKey.BOQ,
    Permissions.CAN_EDIT_BOQ.value: SectionKey.BOQ,
    Permissions.CAN_CREATE_RECCE_LINK.value: SectionKey.RECCE,
    Permissions.CAN_VIEW_DESIGN_FILES.value: SectionKey.DESIGN,
    Permissions.CAN_VIEW_DESIGN_MODULE.value: SectionKey.DESIGN,
    Permissions.CAN_EDIT_DESIGN_FILES.value: SectionKey.DESIGN,
    Permissions.CAN_DOWNLOAD_DESIGN_FILES.value: SectionKey.DESIGN,
    Permissions.CAN_DELETE_DESIGN_FILES.value: SectionKey.DESIGN,
    Permissions.CAN_ASSIGN_PROJECT_USER.value: SectionKey.PROJECT,
    Permissions.CAN_CREATE_OUTGOING_ORDER.value: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_SEND_OUTGOING_ORDER.value: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_CANCEL_OUTGOING_ORDER.value: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_DELETE_OUTGOING_ORDER.value: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_FREEZE_DESIGN.value: SectionKey.DESIGN,
    Permissions.CAN_ACCESS_PROJECT_ATTACHMENT: SectionKey.PROJECT,
    Permissions.CAN_ACCESS_PROJECT_CUSTOM_FIELDS: SectionKey.PROJECT,
    Permissions.CAN_CANCEL_INCOMING_ORDER: SectionKey.BOQ,
    Permissions.CAN_EDIT_PROPOSAL: SectionKey.BOQ,
    Permissions.CAN_VIEW_PROPOSAL_MAIL_BOX: SectionKey.BOQ,
    Permissions.CAN_SEND_PROPOSAL: SectionKey.BOQ,
    Permissions.CAN_APPROVE_PROPOSAL: SectionKey.BOQ,
    Permissions.CAN_EDIT_PROJECT: SectionKey.PROJECT,
    Permissions.CAN_ACCESS_RECCE: SectionKey.RECCE,
    Permissions.CAN_EDIT_RECCE_DATA: SectionKey.RECCE,
    Permissions.CAN_APPROVE_DESIGN_FILES: SectionKey.DESIGN,
    Permissions.CAN_REJECT_DESIGN_FILES: SectionKey.DESIGN,
    Permissions.CAN_MARK_REVIEWED_DESIGN_FILES: SectionKey.DESIGN,
    Permissions.CAN_MARK_CLIENT_REJECT_DESIGN_FILES: SectionKey.DESIGN,
    Permissions.CAN_UPLOAD_RECCE_FILES: SectionKey.RECCE,
    Permissions.CAN_APPROVE_RECCE: SectionKey.RECCE,
    Permissions.CAN_ACCESS_MY_SCOPE: SectionKey.BOQ,
    Permissions.CAN_ACCESS_INCOMING_ORDER: SectionKey.BOQ,
    Permissions.CAN_ACCESS_PROPOSAL_FOR_CLIENT: SectionKey.BOQ,
    Permissions.CAN_ACCESS_OUTGOING_ORDER: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_ACCESS_PROPOSAL_FROM_VENDOR: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_ACCESS_VENDOR_WISE_SCOPE: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_ACCESS_VENDOR_WISE_PROGRESS: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_ACCESS_OTHER_EXPENSE: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_VIEW_ALL_EXPENSE_REQUEST: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_ACCESS_WORK_PROGRESS: SectionKey.WORK_REPORTS,
    Permissions.CAN_UPDATE_WORK_PROGRESS: SectionKey.WORK_REPORTS,
    Permissions.CAN_ACCESS_SNAG: SectionKey.SNAG,
    Permissions.CAN_LINK_SNAG_ITEMS: SectionKey.SNAG,
    Permissions.CAN_UPLOAD_BOQ_ELEMENT_USING_EXCEL: SectionKey.BOQ,
    Permissions.CAN_UPLOAD_GALLERY_FILE_IN_DPR: SectionKey.WORK_REPORTS,
    Permissions.CAN_RECEIVE_PROGRESS_REPORT_NOTIFICATIONS: SectionKey.PROGRESS_UPDATE,
    Permissions.CAN_ACCESS_ORG_SETTINGS: SectionKey.ORG_SETTINGS,
    Permissions.CAN_ACCESS_MANAGE_VENDOR: SectionKey.OTHERS,
    Permissions.CAN_ACCESS_MATERIALS: SectionKey.OTHERS,
    Permissions.CAN_EDIT_MANAGE_VENDOR: SectionKey.OTHERS,
    Permissions.CAN_CHANGE_VENDOR_STATUS: SectionKey.OTHERS,
    Permissions.CAN_APPROVE_CLIENT_PROPOSAL: SectionKey.PROPOSAL_FOR_CLIENT,
    Permissions.CAN_RESTRICT_CLIENT_EDIT_PROPOSAL: SectionKey.PROPOSAL_FROM_VENDOR,
    Permissions.CAN_ACCESS_METABASE_DASHBOARD: SectionKey.OTHERS,
    Permissions.CAN_ACCESS_MANAGE_CLIENT: SectionKey.OTHERS,
    Permissions.CAN_ACCESS_MODULE_CONFIGURATIONS: SectionKey.RECCE,
    Permissions.CAN_EDIT_RECCE_TEMPLATE: SectionKey.RECCE,
    Permissions.CAN_ACCESS_WORK_PROGRESS_MATERIALS: SectionKey.WORK_REPORTS,
    Permissions.CAN_EDIT_CLIENT: SectionKey.OTHERS,
    Permissions.CAN_VIEW_APPROVED_DESIGN_FILES: SectionKey.DESIGN,
    Permissions.CAN_VIEW_REJECTED_DESIGN_FILES: SectionKey.DESIGN,
    Permissions.CAN_VIEW_REVIEWED_DESIGN_FILES: SectionKey.DESIGN,
    Permissions.CAN_VIEW_DRAFT_DESIGN_FILES: SectionKey.DESIGN,
    Permissions.CAN_EDIT_APPROVAL_HIERARCHY: SectionKey.OTHERS,
    Permissions.CAN_VIEW_ALL_TASKS: SectionKey.OTHERS,
    Permissions.CAN_ACCESS_CONSOLIDATED_PROGRESS_REPORT: SectionKey.WORK_REPORTS,
    Permissions.CAN_VIEW_BASE_AMOUNT: SectionKey.OTHERS,
    Permissions.CAN_VIEW_SERVICE_CHARGE: SectionKey.OTHERS,
    Permissions.CAN_EDIT_SERVICE_CHARGE: SectionKey.OTHERS,
    Permissions.CAN_VIEW_DISCOUNT: SectionKey.OTHERS,
    Permissions.CAN_EDIT_DISCOUNT: SectionKey.OTHERS,
    Permissions.CAN_ACCESS_VENDOR_PAYMENT_REQUEST: SectionKey.OTHERS,
    Permissions.CAN_ACCESS_CLIENT_PAYMENT_REQUEST: SectionKey.OTHERS,
    Permissions.CAN_CREATE_VENDOR_PAYMENT_REQUEST: SectionKey.VENDOR_PAYMENTS,
    # Permissions.CAN_CREATE_PAYMENT_ENTRY: SectionKey.VENDOR_PAYMENTS,
    Permissions.CAN_CREATE_PAYMENT_ENTRY: SectionKey.OTHERS,
    Permissions.CAN_CREATE_EXPENSE_PAYMENT_ENTRY: SectionKey.OTHERS,
    Permissions.CAN_ACCESS_EXPORT_BOQ_PDF: SectionKey.BOQ,
    Permissions.CAN_ACCESS_EXPORT_GRN_PDF: SectionKey.INVENTORY,
    Permissions.CAN_ACCESS_EXPORT_AND_SHARE_BOQ_PDF: SectionKey.BOQ,
    Permissions.CAN_PREVIEW_PURCHASE_ORDER: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_CHANGE_PROGRESS_UPDATE_METHOD: SectionKey.PROGRESS_UPDATE,
    Permissions.CAN_ARCHIVE_PROJECTS: SectionKey.PROJECT,
    Permissions.CAN_ACCESS_BOARDS: SectionKey.BOARD,
    Permissions.CAN_EDIT_BOARD: SectionKey.BOARD,
    Permissions.CAN_VIEW_ALL_BOARDS: SectionKey.BOARD,
    Permissions.CAN_VIEW_LEAD_COMPANIES: SectionKey.LEAD,
    Permissions.CAN_VIEW_LEAD_CONTACTS: SectionKey.LEAD,
    Permissions.CAN_CANCEL_PO: SectionKey.OUTGOING_ORDER,
    Permissions.CAN_MARK_EXPENSE_CLOSURE: SectionKey.ORDERS_EXPENSES,
    Permissions.CAN_ACCESS_INSIGHTS: SectionKey.DASHBOARDS,
    Permissions.CAN_CREATE_DASHBOARD_COLLECTIONS: SectionKey.DASHBOARDS,
    Permissions.CAN_RECEIVE_SNAG_NOTIFICATIONS: SectionKey.SNAG,
    Permissions.CAN_MANAGE_DPR_SUBSCRIPTION: SectionKey.WORK_REPORTS,
    Permissions.CAN_VIEW_BUDGET_RATE: SectionKey.ELEMENT_LIBRARY,
    Permissions.CAN_CONFIGURE_PAYMENT_TERMS_FOR_VENDOR_ORDER: SectionKey.ORG_SETTINGS,
    Permissions.CAN_CONFIGURE_OTHER_TERMS_AND_CONDITIONS_FOR_VENDOR_ORDER: SectionKey.ORG_SETTINGS,
    Permissions.CAN_CONFIGURE_TERMS_AND_CONDITIONS_FOR_QUOTATION: SectionKey.ORG_SETTINGS,
    Permissions.CAN_CONFIGURE_CLIENT_VIEW: SectionKey.ORG_SETTINGS,
    Permissions.CAN_ACCESS_PROJECT_ACTIVITY_SCHEDULE: SectionKey.PROJECT_ACTIVITY_SCHEDULE,
    Permissions.CAN_INTEGRATE_CRM_WITH_META: SectionKey.ORG_SETTINGS,
    Permissions.CAN_ACCESS_VENDOR_INVOICES: SectionKey.VENDOR_INVOICES,
    Permissions.CAN_ACCESS_EXPORT_SNAG_PDF: SectionKey.SNAG,
    Permissions.CAN_VIEW_SCHEDULE_PROGRESS_ON_PROJECT_LIST: SectionKey.PROJECT_ACTIVITY_SCHEDULE,
    Permissions.CAN_VIEW_SCOPE_PROGRESS_ON_PROJECT_LIST: SectionKey.MY_SCOPE,
    Permissions.CAN_ASSIGN_USER_ON_MULTIPLE_PROJECT_USER_ROLE: SectionKey.PROJECT,
    Permissions.CAN_GENERATE_PROGRESS_REPORT: SectionKey.WORK_REPORTS,
    Permissions.CAN_EDIT_GENERATE_REPORT_SETTINGS: SectionKey.WORK_REPORTS,
    Permissions.CAN_EDIT_EXPORT_REPORT_SETTINGS: SectionKey.WORK_REPORTS,
    Permissions.CAN_DELETE_PROGRESS_REPORT: SectionKey.WORK_REPORTS,
    Permissions.CAN_EXPORT_PROGRESS_REPORT: SectionKey.WORK_REPORTS,
    Permissions.CAN_DELETE_FILE_IN_DPR: SectionKey.WORK_REPORTS,
    Permissions.CAN_CAPTURE_FILE_IN_DPR: SectionKey.WORK_REPORTS,
    Permissions.CAN_MARK_AND_UNMARK_EXECUTION_COMPLETE: SectionKey.WORK_REPORTS,
    Permissions.CAN_ACCESS_MY_REPORTS: SectionKey.OTHERS,
    Permissions.CAN_COMPARE_BOQ_VS_ORDER: SectionKey.BOQ,
    Permissions.CAN_EDIT_TDS_AMOUNT: SectionKey.VENDOR_INVOICES,
}

NOTIFICATION_PERMISSIONS = {
    Permissions.CAN_RECEIVE_PROJECT_NOTIFICATIONS: _("Project updates"),
    Permissions.CAN_RECEIVE_PROJECT_COMMENT_NOTIFICATIONS: _("Project comments"),
    Permissions.CAN_RECEIVE_RECCE_CREATION_NOTIFICATIONS: _("Recce creation"),
    Permissions.CAN_RECEIVE_RECCE_START_NOTIFICATIONS: _("Recce start"),
    Permissions.CAN_RECEIVE_RECCE_SUBMISSION_NOTIFICATIONS: _("Recce submission"),
    Permissions.CAN_RECEIVE_RECCE_UPDATE_NOTIFICATIONS: _("Recce update"),
    Permissions.CAN_RECEIVE_RECCE_APPROVE_NOTIFICATIONS: _("Recce approve"),
    Permissions.CAN_RECEIVE_DESIGN_APPROVE_NOTIFICATIONS: _("Design approve"),
    Permissions.CAN_RECEIVE_DESIGN_FREEZE_NOTIFICATIONS: _("Design freeze"),
    Permissions.CAN_RECEIVE_INCOMING_ORDER_RECEIVED_NOTIFICATIONS: _("Incoming order received"),
    Permissions.CAN_RECEIVE_INCOMING_ORDER_MODIFIED_NOTIFICATIONS: _("Incoming order modified"),
    Permissions.CAN_RECEIVE_INCOMING_ORDER_COMPLETED_NOTIFICATIONS: _("Incoming order completed"),
    Permissions.CAN_RECEIVE_INCOMING_ORDER_PO_RECEIVED_NOTIFICATIONS: _("Incoming PO received"),
    Permissions.CAN_RECEIVE_INCOMING_ORDER_PO_CANCELLED_NOTIFICATIONS: _("Incoming PO cancelled"),
    Permissions.CAN_RECEIVE_INCOMING_ORDER_CANCEL_NOTIFICATIONS: _("Order cancelled"),
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_SENT_NOTIFICATIONS: _("Outgoing order sent"),
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_COMPLETED_NOTIFICATIONS: _("Outgoing order completed"),
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_PO_SENT_NOTIFICATIONS: _("Outgoing PO sent"),
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_PO_CANCELLED_NOTIFICATIONS: _("Outgoing PO cancelled"),
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_MODIFIED_NOTIFICATIONS: _("Outgoing order modified"),
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_NEW_INVOICE_UPLOADED_NOTIFICATIONS: _("Vendor invoice uploaded"),
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_ALL_INVOICE_MARKED_UPLOADED_NOTIFICATIONS: _("All order invoice upload"),
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_CANCEL_NOTIFICATIONS: _("Order cancelled"),
    Permissions.CAN_RECEIVE_PROGRESS_REPORT_NOTIFICATIONS: _("Progress report"),
    Permissions.CAN_RECEIVE_PROJECT_SHARE_NOTIFICATIONS: _("Project shared"),
    Permissions.CAN_RECEIVE_PROPOSAL_SENT_NOTIFICATIONS: _("Proposal sent"),
    Permissions.CAN_RECEIVE_PROPOSAL_REJECTED_NOTIFICATIONS: _("Proposal rejected"),
    Permissions.CAN_RECEIVE_DESIGN_NEW_VERSION_NOTIFICATION: _("New version uploaded"),
    Permissions.CAN_RECEIVE_DESIGN_NEW_VERSION_POST_FREEZE_UPLOADED_NOTIFICATION: _("New version external uploaded"),
    Permissions.CAN_RECEIVE_SNAG_NOTIFICATIONS: _("Snag assignment"),
    Permissions.CAN_RECEIVE_PROJECT_SCHEDULE_COMPLETED_NOTIFICATIONS: _("Activity completed"),
    Permissions.CAN_RECEIVE_PROJECT_SCHEDULE_DELAYED_NOTIFICATIONS: _("Activity delayed"),
    Permissions.CAN_RECEIVE_PROJECT_SCHEDULE_OVERDUE_NOTIFICATIONS: _("Activity overdue"),
    Permissions.CAN_RECEIVE_PROJECT_SCHEDULE_ACTIVITIES_DELETED_NOTIFICATIONS: _("Activity deleted"),
    Permissions.CAN_RECEIVE_PROJECT_SCHEDULE_ACTIVITIES_ASSIGNED_NOTIFICATIONS: _("Activity assigned"),
}


DIRECT_PROJECT_PERMISSIONS = [
    Permissions.CAN_ACCESS_BOQ,
    Permissions.CAN_EDIT_BOQ,
    Permissions.CAN_CREATE_OUTGOING_ORDER,
    Permissions.CAN_SEND_OUTGOING_ORDER,
    Permissions.CAN_CANCEL_OUTGOING_ORDER,
    Permissions.CAN_DELETE_OUTGOING_ORDER,
    Permissions.CAN_ADD_CUSTOM_BOQ_ELEMENT,
    Permissions.CAN_IMPORT_BOQ_ELEMENT_FROM_PROJECT,
    Permissions.CAN_IMPORT_BOQ_ELEMENT_FROM_LIBRARY,
    Permissions.CAN_ACCESS_ORDER,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_CANCEL_NOTIFICATIONS,
    Permissions.CAN_CANCEL_INCOMING_ORDER,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_CANCEL_NOTIFICATIONS,
    Permissions.CAN_CREATE_PROPOSAL,
    Permissions.CAN_EDIT_PROPOSAL,
    Permissions.CAN_SEND_PROPOSAL,
    Permissions.CAN_APPROVE_PROPOSAL,
    Permissions.CAN_VIEW_CLIENT_RATE,
    Permissions.CAN_VIEW_ORDER_RATE,
    Permissions.CAN_RECEIVE_PROJECT_NOTIFICATIONS,
    Permissions.CAN_CREATE_INCOMING_ORDER,
    Permissions.CAN_ACCESS_MY_SCOPE,
    Permissions.CAN_ACCESS_INCOMING_ORDER,
    Permissions.CAN_ACCESS_PROPOSAL_FOR_CLIENT,
    Permissions.CAN_ACCESS_OUTGOING_ORDER,
    Permissions.CAN_ACCESS_PROPOSAL_FROM_VENDOR,
    Permissions.CAN_ACCESS_VENDOR_WISE_SCOPE,
    Permissions.CAN_ACCESS_VENDOR_WISE_PROGRESS,
    Permissions.CAN_ACCESS_WORK_PROGRESS,
    Permissions.CAN_UPDATE_WORK_PROGRESS,
    Permissions.CAN_RECEIVE_PROJECT_COMMENT_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_RECCE_CREATION_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_RECCE_START_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_RECCE_SUBMISSION_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_RECCE_UPDATE_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_RECCE_APPROVE_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_DESIGN_APPROVE_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_DESIGN_FREEZE_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_RECEIVED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_MODIFIED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_COMPLETED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_PO_RECEIVED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_PO_CANCELLED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_SENT_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_COMPLETED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_PO_SENT_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_PO_CANCELLED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_MODIFIED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_NEW_INVOICE_UPLOADED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_ALL_INVOICE_MARKED_UPLOADED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_PROGRESS_REPORT_NOTIFICATIONS,
    Permissions.CAN_ASSIGN_PROJECT_USER,
    Permissions.CAN_CLOSE_ORDER,
    Permissions.CAN_ACCESS_SNAG,
    Permissions.CAN_UPLOAD_BOQ_ELEMENT_USING_EXCEL,
    Permissions.CAN_RECEIVE_PROPOSAL_SENT_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_PROPOSAL_REJECTED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_DESIGN_NEW_VERSION_NOTIFICATION,
    Permissions.CAN_RECEIVE_DESIGN_NEW_VERSION_POST_FREEZE_UPLOADED_NOTIFICATION,
    Permissions.CAN_UPDATE_PROJECT_STATUS,
    Permissions.CAN_APPROVE_CLIENT_PROPOSAL,
    Permissions.CAN_CLOSE_SNAG,
    Permissions.CAN_ACCESS_OTHER_EXPENSE,
    Permissions.CAN_VIEW_BUDGET_RATE,
    Permissions.CAN_CANCEL_PO,
    Permissions.CAN_CHANGE_PROGRESS_UPDATE_METHOD,
    Permissions.CAN_RECEIVE_SNAG_NOTIFICATIONS,
    Permissions.CAN_VIEW_DESIGN_MODULE,
    Permissions.CAN_ACCESS_MATERIALS,
    Permissions.CAN_CREATE_GRN,
    Permissions.CAN_UPDATE_STOCK_CONSUMPTION,
    Permissions.CAN_CANCEL_STOCK_TRANSFER,
    Permissions.CAN_APPROVE_STOCK_TRANSFER,
    Permissions.CAN_ACCESS_VENDOR_INVOICES,
    Permissions.CAN_GENERATE_PROGRESS_REPORT,
    Permissions.CAN_EDIT_GENERATE_REPORT_SETTINGS,
    Permissions.CAN_EDIT_EXPORT_REPORT_SETTINGS,
    Permissions.CAN_DELETE_PROGRESS_REPORT,
    Permissions.CAN_EXPORT_PROGRESS_REPORT,
    Permissions.CAN_DELETE_FILE_IN_DPR,
    Permissions.CAN_CAPTURE_FILE_IN_DPR,
    Permissions.CAN_MARK_AND_UNMARK_EXECUTION_COMPLETE,
    Permissions.CAN_UPLOAD_GALLERY_FILE_IN_DPR,
    Permissions.CAN_MANAGE_DPR_SUBSCRIPTION,
    Permissions.CAN_ACCESS_VENDOR_PAYMENT_REQUEST,
    Permissions.CAN_CREATE_VENDOR_PAYMENT_REQUEST,
]

DIRECT_CORE_PERMISSIONS = [
    Permissions.CAN_ACCESS_ORDER,
    Permissions.CAN_EDIT_ELEMENT_LIBRARY_GLOBAL_SCOPE,
    Permissions.CAN_EDIT_ELEMENT_LIBRARY_LOCAL_SCOPE,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_SENT_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_COMPLETED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_PO_SENT_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_PO_CANCELLED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_MODIFIED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_NEW_INVOICE_UPLOADED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_ALL_INVOICE_MARKED_UPLOADED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_OUTGOING_ORDER_CANCEL_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_RECEIVED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_MODIFIED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_COMPLETED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_PO_RECEIVED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_PO_CANCELLED_NOTIFICATIONS,
    Permissions.CAN_RECEIVE_INCOMING_ORDER_CANCEL_NOTIFICATIONS,
    Permissions.CAN_EDIT_ORG_USER,
    Permissions.CAN_EDIT_PROJECT_ROLE,
    Permissions.CAN_CREATE_RECCE_LINK,
    Permissions.CAN_VIEW_DESIGN_FILES,
    Permissions.CAN_VIEW_DESIGN_MODULE,
    Permissions.CAN_EDIT_DESIGN_FILES,
    Permissions.CAN_DOWNLOAD_DESIGN_FILES,
    Permissions.CAN_DELETE_DESIGN_FILES,
    Permissions.CAN_FREEZE_DESIGN,
    Permissions.CAN_ACCESS_PROJECT_ATTACHMENT,
    Permissions.CAN_ACCESS_PROJECT_CUSTOM_FIELDS,
    Permissions.CAN_EDIT_PROJECT,
    Permissions.CAN_ACCESS_RECCE,
    Permissions.CAN_EDIT_RECCE_DATA,
    Permissions.CAN_APPROVE_DESIGN_FILES,
    Permissions.CAN_REJECT_DESIGN_FILES,
    Permissions.CAN_MARK_REVIEWED_DESIGN_FILES,
    Permissions.CAN_MARK_CLIENT_REJECT_DESIGN_FILES,
    Permissions.CAN_UPLOAD_RECCE_FILES,
    Permissions.CAN_APPROVE_RECCE,
    Permissions.CAN_ACCESS_BOQ,
    Permissions.CAN_EDIT_BOQ,
    Permissions.CAN_ASSIGN_PROJECT_USER,
    Permissions.CAN_CREATE_OUTGOING_ORDER,
    Permissions.CAN_SEND_OUTGOING_ORDER,
    Permissions.CAN_CANCEL_OUTGOING_ORDER,
    Permissions.CAN_DELETE_OUTGOING_ORDER,
    Permissions.CAN_VIEW_CLIENT_RATE,
    Permissions.CAN_VIEW_BASE_AMOUNT,
    Permissions.CAN_VIEW_SERVICE_CHARGE,
    Permissions.CAN_EDIT_SERVICE_CHARGE,
    Permissions.CAN_VIEW_DISCOUNT,
    Permissions.CAN_EDIT_DISCOUNT,
    Permissions.CAN_VIEW_ORDER_RATE,
    Permissions.CAN_EDIT_PROPOSAL,
    Permissions.CAN_SEND_PROPOSAL,
    Permissions.CAN_APPROVE_PROPOSAL,
    Permissions.CAN_ACCESS_MY_SCOPE,
    Permissions.CAN_ACCESS_INCOMING_ORDER,
    Permissions.CAN_ACCESS_PROPOSAL_FOR_CLIENT,
    Permissions.CAN_ACCESS_OUTGOING_ORDER,
    Permissions.CAN_ACCESS_PROPOSAL_FROM_VENDOR,
    Permissions.CAN_ACCESS_VENDOR_WISE_SCOPE,
    Permissions.CAN_ACCESS_VENDOR_WISE_PROGRESS,
    Permissions.CAN_ACCESS_WORK_PROGRESS,
    Permissions.CAN_UPDATE_WORK_PROGRESS,
    Permissions.CAN_VIEW_ALL_PROJECTS,
    Permissions.CAN_RECEIVE_PROJECT_SHARE_NOTIFICATIONS,
    Permissions.CAN_ACCESS_SNAG,
    Permissions.CAN_RECEIVE_PROGRESS_REPORT_NOTIFICATIONS,
    Permissions.CAN_ACCESS_ORG_SETTINGS,
    Permissions.CAN_ACCESS_MODULE_CONFIGURATIONS,
    Permissions.CAN_EDIT_RECCE_TEMPLATE,
    Permissions.CAN_VIEW_APPROVED_DESIGN_FILES,
    Permissions.CAN_VIEW_REJECTED_DESIGN_FILES,
    Permissions.CAN_VIEW_REVIEWED_DESIGN_FILES,
    Permissions.CAN_VIEW_DRAFT_DESIGN_FILES,
    Permissions.CAN_VIEW_ALL_TASKS,
    Permissions.CAN_CHANGE_VENDOR_STATUS,
    Permissions.CAN_ARCHIVE_PROJECTS,
    Permissions.CAN_CREATE_GRN,
    Permissions.CAN_UPDATE_STOCK_CONSUMPTION,
    Permissions.CAN_CANCEL_STOCK_TRANSFER,
    Permissions.CAN_APPROVE_STOCK_TRANSFER,
    Permissions.CAN_CREATE_PAYMENT_ENTRY,
    Permissions.CAN_CREATE_EXPENSE_PAYMENT_ENTRY,
    Permissions.CAN_EDIT_BOARD,
    Permissions.CAN_VIEW_ALL_BOARDS,
    Permissions.CAN_RECEIVE_SNAG_NOTIFICATIONS,
    Permissions.CAN_VIEW_BUDGET_RATE,
    Permissions.CAN_CONFIGURE_PAYMENT_TERMS_FOR_VENDOR_ORDER,
    Permissions.CAN_CONFIGURE_OTHER_TERMS_AND_CONDITIONS_FOR_VENDOR_ORDER,
    Permissions.CAN_CONFIGURE_TERMS_AND_CONDITIONS_FOR_QUOTATION,
    Permissions.CAN_ACCESS_PROJECT_ACTIVITY_SCHEDULE,
    Permissions.CAN_UPDATE_PROJECT_ACTIVITY_SCHEDULE,
    Permissions.CAN_VIEW_SCHEDULE_PROGRESS_ON_PROJECT_LIST,
    Permissions.CAN_VIEW_SCOPE_PROGRESS_ON_PROJECT_LIST,
    Permissions.CAN_EDIT_PROJECT_ACTIVITY_SCHEDULE,
    Permissions.CAN_ASSIGN_USER_ON_MULTIPLE_PROJECT_USER_ROLE,
    Permissions.CAN_ACCESS_VENDOR_INVOICES,
    Permissions.CAN_VIEW_ALL_EXPENSE_REQUEST,
    Permissions.CAN_VIEW_LEAD_CONTACTS,
    Permissions.CAN_VIEW_LEAD_COMPANIES,
    Permissions.CAN_EDIT_GENERATE_REPORT_SETTINGS,
    Permissions.CAN_EDIT_EXPORT_REPORT_SETTINGS,
    Permissions.CAN_ACCESS_MY_REPORTS,
    Permissions.CAN_UPLOAD_GALLERY_FILE_IN_DPR,
    Permissions.CAN_MANAGE_DPR_SUBSCRIPTION,
    Permissions.CAN_DELETE_PROGRESS_REPORT,
    Permissions.CAN_ACCESS_VENDOR_PAYMENT_REQUEST,
    Permissions.CAN_CREATE_VENDOR_PAYMENT_REQUEST,
    Permissions.CAN_EDIT_TDS_AMOUNT,
]

ACTION_PERMISSION_MAPPING = {
    Actions.NOTIFY_PROJECT_CREATION: Permissions.CAN_ASSIGN_PROJECT_USER,
    Actions.NOTIFY_PROJECT_DATE_CHANGED: Permissions.CAN_RECEIVE_PROJECT_NOTIFICATIONS,
    Actions.NOTIFY_PROJECT_DATE_ASSIGN: Permissions.CAN_RECEIVE_PROJECT_NOTIFICATIONS,
    Actions.NOTIFY_RECCE_LINK_CREATION: Permissions.CAN_RECEIVE_RECCE_CREATION_NOTIFICATIONS,
    Actions.NOTIFY_RECCE_ASSIGNED: Permissions.CAN_RECEIVE_RECCE_CREATION_NOTIFICATIONS,
    Actions.NOTIFY_RECCE_STARTED: Permissions.CAN_RECEIVE_RECCE_START_NOTIFICATIONS,
    Actions.NOTIFY_RECCE_SUBMISSION: Permissions.CAN_RECEIVE_RECCE_SUBMISSION_NOTIFICATIONS,
    Actions.NOTIFY_ORDER_SENT: Permissions.CAN_RECEIVE_OUTGOING_ORDER_SENT_NOTIFICATIONS,
    Actions.NOTIFY_RECCE_UPDATED: Permissions.CAN_RECEIVE_RECCE_UPDATE_NOTIFICATIONS,
    Actions.NOTIFY_RECCE_APPROVED: Permissions.CAN_RECEIVE_RECCE_APPROVE_NOTIFICATIONS,
    Actions.NOTIFY_ORDER_RECEIEVED: Permissions.CAN_RECEIVE_INCOMING_ORDER_RECEIVED_NOTIFICATIONS,
    Actions.NOTIFY_ORDER_RECEIEVED_COMPLETED: Permissions.CAN_RECEIVE_INCOMING_ORDER_COMPLETED_NOTIFICATIONS,
    Actions.NOTIFY_ORDER_SENT_COMPLETED: Permissions.CAN_RECEIVE_OUTGOING_ORDER_COMPLETED_NOTIFICATIONS,
    Actions.NOTIFY_DESIGN_APPROVED: Permissions.CAN_RECEIVE_DESIGN_APPROVE_NOTIFICATIONS,
    Actions.NOTIFY_DESIGN_FREEZE: Permissions.CAN_RECEIVE_DESIGN_FREEZE_NOTIFICATIONS,
    Actions.NOTIFY_PO_RECEIVED: Permissions.CAN_RECEIVE_INCOMING_ORDER_PO_RECEIVED_NOTIFICATIONS,
    Actions.NOTIFY_PO_SENT: Permissions.CAN_RECEIVE_OUTGOING_ORDER_PO_SENT_NOTIFICATIONS,
    Actions.NOTIFY_PO_SENT_CANCELLED: Permissions.CAN_RECEIVE_OUTGOING_ORDER_PO_CANCELLED_NOTIFICATIONS,
    Actions.NOTIFY_PO_RECEIVED_CANCELLED: Permissions.CAN_RECEIVE_INCOMING_ORDER_PO_CANCELLED_NOTIFICATIONS,
    Actions.NOTIFY_ORDER_SENT_MODIFIED: Permissions.CAN_RECEIVE_OUTGOING_ORDER_MODIFIED_NOTIFICATIONS,
    Actions.NOTIFY_ORDER_RECEIVED_MODIFIED: Permissions.CAN_RECEIVE_INCOMING_ORDER_MODIFIED_NOTIFICATIONS,
    Actions.NOTIFY_ORDER_SENT_CANCELLED: Permissions.CAN_RECEIVE_OUTGOING_ORDER_CANCEL_NOTIFICATIONS,
    Actions.NOTIFY_ORDER_RECEIVED_CANCELLED: Permissions.CAN_RECEIVE_INCOMING_ORDER_CANCEL_NOTIFICATIONS,
    Actions.NOTIFY_NEW_INVOICE_UPLOADED: Permissions.CAN_RECEIVE_OUTGOING_ORDER_NEW_INVOICE_UPLOADED_NOTIFICATIONS,
    Actions.NOTIFY_ALL_INVOICES_MARKED_UPLOADED: Permissions.CAN_RECEIVE_OUTGOING_ORDER_ALL_INVOICE_MARKED_UPLOADED_NOTIFICATIONS,  # noqa
    Actions.NOTIFY_PROJECT_COMMENT: Permissions.CAN_RECEIVE_PROJECT_COMMENT_NOTIFICATIONS,
    Actions.NOTIFY_PROJECT_COMMENT_APPROVAL_ACCEPTED: Permissions.CAN_RECEIVE_PROJECT_COMMENT_NOTIFICATIONS,
    Actions.NOTIFY_PROJECT_COMMENT_APPROVAL_REJECTED: Permissions.CAN_RECEIVE_PROJECT_COMMENT_NOTIFICATIONS,
    Actions.NOTIFY_PROJECT_COMMENT_APPROVAL_REQUESTED: Permissions.CAN_RECEIVE_PROJECT_COMMENT_NOTIFICATIONS,
    Actions.NOTIFY_PROJECT_COMMENT_MENTIONED: Permissions.CAN_RECEIVE_PROJECT_COMMENT_NOTIFICATIONS,
    Actions.NOTIFY_WORK_PROGRESS_REPORT_PDF_GENERATED: Permissions.CAN_RECEIVE_PROGRESS_REPORT_NOTIFICATIONS,
    Actions.NOTIFY_WORK_PROGRESS_SUBSCRIBERS_REPORT_PDF_GENERATED: Permissions.CAN_RECEIVE_PROGRESS_REPORT_NOTIFICATIONS,
    Actions.NOTIFY_PROJECT_SHARED: Permissions.CAN_RECEIVE_PROJECT_SHARE_NOTIFICATIONS,
    Actions.NOTIFY_MARK_EXECUTION_COMPLETED: Permissions.CAN_RECEIVE_PROGRESS_REPORT_NOTIFICATIONS,
    Actions.NOTIFY_CLIENT_FOR_WORK_PROGRESS_REPORT_PDF_GENERATED: Permissions.CAN_RECEIVE_PROGRESS_REPORT_NOTIFICATIONS,
    Actions.NOTIFY_CLIENT_SUBSCRIBERS_FOR_WORK_PROGRESS_REPORT_PDF_GENERATED: Permissions.CAN_RECEIVE_PROGRESS_REPORT_NOTIFICATIONS,
    Actions.NOTIFY_PROPOSAL_SENT: Permissions.CAN_RECEIVE_PROPOSAL_SENT_NOTIFICATIONS,
    Actions.NOTIFY_PROPOSAL_REJECTED: Permissions.CAN_RECEIVE_PROPOSAL_REJECTED_NOTIFICATIONS,
    Actions.NOTIFY_DESIGN_NEW_VERSION_UPLOADED: Permissions.CAN_RECEIVE_DESIGN_NEW_VERSION_NOTIFICATION,
    Actions.NOTIFY_DESIGN_NEW_VERSION_POST_FREEZE_UPLOADED: Permissions.CAN_RECEIVE_DESIGN_NEW_VERSION_POST_FREEZE_UPLOADED_NOTIFICATION,  # noqa
    Actions.NOTIFY_STOCK_TRANSFER_BATCH_CREATED: Permissions.CAN_APPROVE_STOCK_TRANSFER,
    Actions.NOTIFY_STOCK_TRANSFER_BATCH_ACTION: Permissions.CAN_APPROVE_STOCK_TRANSFER,
    Actions.NOTIFY_SNAG_ASSIGNMENT_TO_ASSIGNEE: Permissions.CAN_RECEIVE_SNAG_NOTIFICATIONS,
    Actions.NOTIFY_SNAG_ASSIGNMENT_TO_CREATOR: Permissions.CAN_RECEIVE_SNAG_NOTIFICATIONS,
    Actions.NOTIFY_SNAG_ALLOT_AND_COMMIT_TIMELINE_TO_OBSERVERS: Permissions.CAN_RECEIVE_SNAG_NOTIFICATIONS,
    Actions.NOTIFY_SNAG_ALLOT_AND_COMMIT_TIMELINE_TO_ALLOTEE: Permissions.CAN_RECEIVE_SNAG_NOTIFICATIONS,
    Actions.NOTIFY_SNAG_BULK_ASSIGNMENT_TO_ASSIGNEE: Permissions.CAN_RECEIVE_SNAG_NOTIFICATIONS,
    Actions.NOTIFY_SNAG_BULK_ASSIGNMENT_TO_CREATORS: Permissions.CAN_RECEIVE_SNAG_NOTIFICATIONS,
    Actions.NOTIFY_SNAG_UNRESOLVED: Permissions.CAN_RECEIVE_SNAG_NOTIFICATIONS,
    Actions.NOTIFY_SNAG_BULK_ALLOT_AND_COMMIT_TIMELINE_TO_OBSERVERS: Permissions.CAN_RECEIVE_SNAG_NOTIFICATIONS,
    Actions.NOTIFY_SNAG_BULK_ALLOT_AND_TIMELINE_COMMIT_TO_ALLOTEE: Permissions.CAN_RECEIVE_SNAG_NOTIFICATIONS,
    Actions.NOTIFY_PROJECT_SCHEDULE_COMPLETED: Permissions.CAN_RECEIVE_PROJECT_SCHEDULE_COMPLETED_NOTIFICATIONS,  # noqa
    Actions.NOTIFY_PROJECT_SCHEDULE_DELAY: Permissions.CAN_RECEIVE_PROJECT_SCHEDULE_DELAYED_NOTIFICATIONS,
    Actions.NOTIFY_PROJECT_SCHEDULE_OVERDUE: Permissions.CAN_RECEIVE_PROJECT_SCHEDULE_OVERDUE_NOTIFICATIONS,
}


def check_permission_section_mappings():
    if set(Permissions.values) != set(PROJECT_PERMISSION_SECTION_MAPPINGS.keys()).union(
        set(CORE_PERMISSION_SECTION_MAPPINGS.keys())
    ):
        raise Exception("Permission section mappings does not contain all permissions choices.")


def get_action_permissions(action: Actions) -> str:
    return ACTION_PERMISSION_MAPPING[action]


class PermissionType(TextChoices):
    ADMIN = "admin"
    ORG_USER = "org_user"
    BOARD_USER = "board_user"
