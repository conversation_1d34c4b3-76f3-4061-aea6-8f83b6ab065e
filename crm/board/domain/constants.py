from django.db.models import TextChoices

from common.constants import BaseEnum, CustomFieldTypeEnum
from crm.board.domain.entities import (
    BoardLeadFieldEntity,
    BoardLeadSectionEntity,
    DefaultBoardStageEntity,
)
from crm.board.domain.enums import (
    BoardPermissionEnum,
    BoardUserCreationSettingEnum,
    BoardUserDeleteSettingEnum,
    BoardUserEditSettingEnum,
    BoardUserSettingEnum,
    BoardUserViewSettingEnum,
)


class BoardStageActionEnum(BaseEnum):
    MOVE = "move"
    DELETE = "delete"
    RENAME = "rename"


class BoardLeadSectionActionEnum(BaseEnum):
    ARCHIVE = "archive"
    MOVE = "move"
    DELETE = "delete"
    ADD_FIELD = "add_field"
    EDIT = "edit"
    ADD_SECTION = "add_section"


class BoardLeadFieldActionEnum(BaseEnum):
    ARCHIVE = "archive"
    MOVE = "move"
    EDIT = "edit"
    DELETE = "delete"
    REQUIRED = "required"


DEFAULT_STAGE_DATA = [
    DefaultBoardStageEntity(
        name="New Leads",
        position=1,
    ),
    DefaultBoardStageEntity(
        name="Disqualified",
        position=2,
    ),
    DefaultBoardStageEntity(
        name="Responding",
        position=3,
    ),
    DefaultBoardStageEntity(
        name="Not Responding",
        position=4,
    ),
    DefaultBoardStageEntity(
        name="Quotation Shared",
        position=5,
    ),
    DefaultBoardStageEntity(
        name="Hot",
        position=6,
    ),
    DefaultBoardStageEntity(
        name="Nurture",
        position=7,
    ),
    DefaultBoardStageEntity(
        name="Won",
        position=8,
    ),
]

BASIC_DETAIL_SECTION_UUID = "559b3fd2-38f4-4d33-a1ab-5c87b1330132"
COMPANY_DETAIL_SECTION_UUID = "e1ca7ecc-d1db-4f99-842f-c1e1eb32fb1b"


LEAD_NUMBER_UUID = "dc3ffb25-90ef-4d12-910e-9236bb59dfa5"
LEAD_NAME_UUID = "87cdd8a2-4025-4f5e-9bc4-33b177832b9a"
LEAD_VALUE_UUID = "72ff0b46-b9f3-451f-801d-ec5cdac5c6e4"
LEAD_COMPANY_UUID = "e87e0867-291c-443e-bbad-34c30e977d7b"
LEAD_CITY_UUID = "9c1f3006-b0f1-4e36-923a-53cef68f1477"
LEAD_STATE_UUID = "71955bcd-750b-42f0-a331-604ddd11e5b7"
LEAD_COUNTRY_UUID = "d770b79b-ce99-45e7-8a17-87ded792fbe7"
LEAD_ZIPCODE_UUID = "f3d54348-ce6e-45db-b545-d7555a5f6c2b"
LEAD_ASSINEE_UUID = "29965f42-b401-4462-89cc-b7771fa882bf"
LEAD_CONTACT_UUID = "a6278800-34c6-47bc-9821-b4758d329e46"
LEAD_ADDRESS_LINE_1_UUID = "fd8b0e73-6399-4c20-9bc3-493cc63d8f21"
LEAD_ADDRESS_LINE_2_UUID = "975c3065-da60-4b25-931f-9631238cc5b6"

LEAD_CREATION_DATE_UUID = "4e3ad7cb-155d-4e9e-901f-335ffb15aa8d"
LEAD_STAGE_NAME_UUID = "2cfc196d-585f-464b-9743-34adcc545610"

LEAD_SYSTEM_SECTION_UUIDS = [BASIC_DETAIL_SECTION_UUID, COMPANY_DETAIL_SECTION_UUID]

LEAD_SYSTEM_LEVEL_UNEDITABLE_FIELD_UUIDS = [LEAD_NAME_UUID]

# Lead field that can not be edited
BOARD_LEAD_NON_EDITABLE_FIELD_UUIDS = [LEAD_NAME_UUID]


LEAD_SYSTEM_FIELD_UUIDS = [
    LEAD_NUMBER_UUID,
    LEAD_NAME_UUID,
    LEAD_VALUE_UUID,
    LEAD_COMPANY_UUID,
    LEAD_CITY_UUID,
    LEAD_STATE_UUID,
    LEAD_COUNTRY_UUID,
    LEAD_ZIPCODE_UUID,
    LEAD_ASSINEE_UUID,
    LEAD_CONTACT_UUID,
    LEAD_ADDRESS_LINE_1_UUID,
    LEAD_ADDRESS_LINE_2_UUID,
]

LEAD_CONVERT_TO_PROJECT_FIELD_UUIDS = [
    LEAD_CITY_UUID,
    LEAD_STATE_UUID,
    LEAD_COUNTRY_UUID,
    LEAD_ZIPCODE_UUID,
    LEAD_ADDRESS_LINE_1_UUID,
    LEAD_ADDRESS_LINE_2_UUID,
]


# these fields can not be disabled in board card settings
BOARD_CARD_SETTING_DISABLED_FIELD_UUIDS = [
    LEAD_NAME_UUID,
    LEAD_VALUE_UUID,
    LEAD_COMPANY_UUID,
    LEAD_ASSINEE_UUID,
    LEAD_CONTACT_UUID,
]


DEFAULT_SECTION_DATA = [
    BoardLeadSectionEntity(
        name="Basic Details",
        position=1,
        uuid=BASIC_DETAIL_SECTION_UUID,
        fields=[
            BoardLeadFieldEntity(
                name="Lead Name",
                uuid=LEAD_NAME_UUID,
                position=1,
                is_required=True,
                is_visible=True,
                type=CustomFieldTypeEnum.LEAD_NAME.value,
            ),
            BoardLeadFieldEntity(
                name="Assignee",
                uuid=LEAD_ASSINEE_UUID,
                position=2,
                is_required=False,
                is_visible=True,
                type=CustomFieldTypeEnum.LEAD_ASSIGNEE.value,
            ),
            BoardLeadFieldEntity(
                name="Lead Value",
                uuid=LEAD_VALUE_UUID,
                position=3,
                is_required=False,
                is_visible=True,
                type=CustomFieldTypeEnum.LEAD_VALUE.value,
            ),
        ],
    ),
    BoardLeadSectionEntity(
        name="Company Details",
        position=2,
        uuid=COMPANY_DETAIL_SECTION_UUID,
        fields=[
            BoardLeadFieldEntity(
                name="Company Name",
                uuid=LEAD_COMPANY_UUID,
                position=1,
                is_required=False,
                is_visible=True,
                type=CustomFieldTypeEnum.LEAD_COMPANY.value,
            ),
            BoardLeadFieldEntity(
                name="Contact Details",
                uuid=LEAD_CONTACT_UUID,
                position=2,
                is_required=False,
                is_visible=True,
                type=CustomFieldTypeEnum.LEAD_CONTACT.value,
            ),
        ],
    ),
]


DEFAULT_SECTION_DATA_FOR_PROJECT_TYPE_LEADS = [
    BoardLeadSectionEntity(
        name="Basic Details",
        position=1,
        uuid=BASIC_DETAIL_SECTION_UUID,
        fields=[
            BoardLeadFieldEntity(
                name="Lead Name",
                uuid=LEAD_NAME_UUID,
                position=1,
                is_required=True,
                is_visible=True,
                type=CustomFieldTypeEnum.LEAD_NAME.value,
            ),
            BoardLeadFieldEntity(
                name="Assignee",
                uuid=LEAD_ASSINEE_UUID,
                position=2,
                is_required=False,
                is_visible=True,
                type=CustomFieldTypeEnum.LEAD_ASSIGNEE.value,
            ),
            BoardLeadFieldEntity(
                name="Lead Value",
                uuid=LEAD_VALUE_UUID,
                position=3,
                is_required=False,
                is_visible=True,
                type=CustomFieldTypeEnum.LEAD_VALUE.value,
            ),
            BoardLeadFieldEntity(
                name="Country",
                uuid=LEAD_COUNTRY_UUID,
                position=4,
                is_required=False,
                is_visible=True,
                type=CustomFieldTypeEnum.LEAD_COUNTRY.value,
            ),
            BoardLeadFieldEntity(
                name="State",
                uuid=LEAD_STATE_UUID,
                position=5,
                is_required=False,
                is_visible=True,
                type=CustomFieldTypeEnum.LEAD_STATE.value,
            ),
            BoardLeadFieldEntity(
                name="City",
                uuid=LEAD_CITY_UUID,
                position=6,
                is_required=False,
                is_visible=True,
                type=CustomFieldTypeEnum.LEAD_CITY.value,
            ),
            BoardLeadFieldEntity(
                name="Address Line 1",
                uuid=LEAD_ADDRESS_LINE_1_UUID,
                position=7,
                is_required=False,
                is_visible=True,
                type=CustomFieldTypeEnum.LEAD_ADDRESS.value,
            ),
            BoardLeadFieldEntity(
                name="Address Line 2",
                uuid=LEAD_ADDRESS_LINE_2_UUID,
                position=8,
                is_required=False,
                is_visible=True,
                type=CustomFieldTypeEnum.LEAD_ADDRESS_LINE_2.value,
            ),
            BoardLeadFieldEntity(
                name="Zipcode",
                uuid=LEAD_ZIPCODE_UUID,
                position=9,
                is_required=False,
                is_visible=True,
                type=CustomFieldTypeEnum.LEAD_ZIPCODE.value,
            ),
        ],
    ),
    BoardLeadSectionEntity(
        name="Company Details",
        position=2,
        uuid=COMPANY_DETAIL_SECTION_UUID,
        fields=[
            BoardLeadFieldEntity(
                name="Company Name",
                uuid=LEAD_COMPANY_UUID,
                position=1,
                is_required=False,
                is_visible=True,
                type=CustomFieldTypeEnum.LEAD_COMPANY.value,
            ),
            BoardLeadFieldEntity(
                name="Contact Details",
                uuid=LEAD_CONTACT_UUID,
                position=2,
                is_required=False,
                is_visible=True,
                type=CustomFieldTypeEnum.LEAD_CONTACT.value,
            ),
        ],
    ),
]

LEAD_SYSTEM_FIELD_NAMES_UUID_MAPPING = {
    CustomFieldTypeEnum.LEAD_NAME.value: LEAD_NAME_UUID,
    CustomFieldTypeEnum.LEAD_VALUE.value: LEAD_VALUE_UUID,
    CustomFieldTypeEnum.LEAD_COMPANY.value: LEAD_COMPANY_UUID,
    CustomFieldTypeEnum.LEAD_CITY.value: LEAD_CITY_UUID,
    CustomFieldTypeEnum.LEAD_STATE.value: LEAD_STATE_UUID,
    CustomFieldTypeEnum.LEAD_COUNTRY.value: LEAD_COUNTRY_UUID,
    CustomFieldTypeEnum.LEAD_ZIPCODE.value: LEAD_ZIPCODE_UUID,
    CustomFieldTypeEnum.LEAD_ASSIGNEE.value: LEAD_ASSINEE_UUID,
    CustomFieldTypeEnum.LEAD_CONTACT.value: LEAD_CONTACT_UUID,
    CustomFieldTypeEnum.LEAD_ADDRESS.value: LEAD_ADDRESS_LINE_1_UUID,
    CustomFieldTypeEnum.LEAD_ADDRESS_LINE_2.value: LEAD_ADDRESS_LINE_2_UUID,
}


BOARD_USER_PERMISSION_CONFIG = {
    BoardUserViewSettingEnum.ONLY_CREATED_LEADS.value: "Only Created Leads",
    BoardUserViewSettingEnum.ASSIGNED_AND_CREATED_LEADS.value: "Assigned and Created Leads",
    BoardUserViewSettingEnum.VIEW_ALL_LEADS.value: "All Leads",
    BoardUserCreationSettingEnum.CAN_CREATE_LEADS.value: "Can Create Leads",
    BoardUserEditSettingEnum.ONLY_CREATED_LEADS.value: "Only Created Leads",
    BoardUserEditSettingEnum.ASSIGNED_AND_CREATED_LEADS.value: "Assigned and Created Leads",
    # BoardUserEditSettingEnum.ONLY_ASSIGNED_LEADS.value: "Only Assigned Leads",
    BoardUserEditSettingEnum.EDIT_ALL_LEADS.value: "All Leads",
    BoardUserDeleteSettingEnum.ONLY_CREATED_LEADS.value: "Only Created Leads",
    BoardUserDeleteSettingEnum.ASSIGNED_AND_CREATED_LEADS.value: "Assigned and Created Leads",
    BoardUserDeleteSettingEnum.DELETE_ALL_LEADS.value: "All Leads",
}

BOARD_USER_VIEW_SETTING_NAME = "View Settings"
BOARD_USER_CREATE_SETTING_NAME = "Creation Settings"
BOARD_USER_EDIT_SETTING_NAME = "Edit Settings"
BOARD_USER_DELETE_SETTING_NAME = "Delete Settings"


BOARD_USER_VIEW_SETTING_CONFIG = {
    "name": BOARD_USER_VIEW_SETTING_NAME,
    "key": BoardUserSettingEnum.VIEW_SETTING.value,
    "allowed_values": [
        BoardUserViewSettingEnum.ONLY_CREATED_LEADS.value,
        BoardUserViewSettingEnum.ASSIGNED_AND_CREATED_LEADS.value,
        BoardUserViewSettingEnum.VIEW_ALL_LEADS.value,
        None,
    ],
    "default_value": BoardUserViewSettingEnum.VIEW_ALL_LEADS.value,
}

BOARD_USER_CREATION_SETTING_CONFIG = {
    "name": BOARD_USER_CREATE_SETTING_NAME,
    "key": BoardUserSettingEnum.CREATE_SETTING.value,
    "allowed_values": [BoardUserCreationSettingEnum.CAN_CREATE_LEADS.value, None],
    "default_value": BoardUserCreationSettingEnum.CAN_CREATE_LEADS.value,
}

BOARD_USER_EDIT_SETTING_CONFIG = {
    "name": BOARD_USER_EDIT_SETTING_NAME,
    "key": BoardUserSettingEnum.EDIT_SETTING.value,
    "allowed_values": [
        BoardUserEditSettingEnum.ONLY_CREATED_LEADS.value,
        BoardUserEditSettingEnum.ASSIGNED_AND_CREATED_LEADS.value,
        BoardUserEditSettingEnum.EDIT_ALL_LEADS.value,
        None,
    ],
    "default_value": BoardUserEditSettingEnum.EDIT_ALL_LEADS.value,
}
BOARD_USER_DELETE_SETTING_CONFIG = {
    "name": BOARD_USER_DELETE_SETTING_NAME,
    "key": BoardUserSettingEnum.DELETE_SETTING.value,
    "allowed_values": [
        BoardUserDeleteSettingEnum.ONLY_CREATED_LEADS.value,
        BoardUserDeleteSettingEnum.ASSIGNED_AND_CREATED_LEADS.value,
        BoardUserDeleteSettingEnum.DELETE_ALL_LEADS.value,
        None,
    ],
    "default_value": BoardUserDeleteSettingEnum.DELETE_ALL_LEADS.value,
}
BOARD_USER_SETTING_CONFIG = [
    BOARD_USER_VIEW_SETTING_CONFIG,
    BOARD_USER_CREATION_SETTING_CONFIG,
    BOARD_USER_EDIT_SETTING_CONFIG,
    BOARD_USER_DELETE_SETTING_CONFIG,
]

BOARD_USER_CONFIG = {
    "permission_config": BOARD_USER_PERMISSION_CONFIG,
    "setting_config": BOARD_USER_SETTING_CONFIG,
}


class BoardPermissionChoices(TextChoices):
    CAN_VIEW_CREATED_LEADS = BoardPermissionEnum.CAN_VIEW_CREATED_LEADS.value, "Can View Created Leads"
    CAN_VIEW_ASSIGNED_LEADS = BoardPermissionEnum.CAN_VIEW_ASSIGNED_LEADS.value, "Can View Assigned Leads"
    CAN_VIEW_OTHER_LEADS = BoardPermissionEnum.CAN_VIEW_OTHER_LEADS.value, "Can View Other Leads"

    CAN_CREATE_LEADS = BoardPermissionEnum.CAN_CREATE_LEADS.value, "Can Create Leads"

    CAN_EDIT_CREATED_LEADS = BoardPermissionEnum.CAN_EDIT_CREATED_LEADS.value, "Can Edit Created Leads"
    CAN_EDIT_ASSIGNED_LEADS = (
        BoardPermissionEnum.CAN_EDIT_ASSIGNED_LEADS.value,
        "Can Edit Assigned Leads",
    )
    CAN_EDIT_OTHER_LEADS = BoardPermissionEnum.CAN_EDIT_OTHER_LEADS.value, "Can Edit Other Leads"

    CAN_DELETE_CREATED_LEADS = BoardPermissionEnum.CAN_DELETE_CREATED_LEADS.value, "Can Delete Created Leads"
    CAN_DELETE_ASSIGNED_LEADS = (
        BoardPermissionEnum.CAN_DELETE_ASSIGNED_LEADS.value,
        "Can Delete Assigned Leads",
    )
    CAN_DELETE_OTHER_LEADS = BoardPermissionEnum.CAN_DELETE_OTHER_LEADS.value, "Can Delete Other Leads"


BOARD_VIEW_SETTING_MAPPING: dict[str, list[BoardPermissionEnum]] = {
    BoardUserViewSettingEnum.ONLY_CREATED_LEADS.value: [BoardPermissionEnum.CAN_VIEW_CREATED_LEADS],
    BoardUserViewSettingEnum.ASSIGNED_AND_CREATED_LEADS.value: [
        BoardPermissionEnum.CAN_VIEW_ASSIGNED_LEADS,
        BoardPermissionEnum.CAN_VIEW_CREATED_LEADS,
    ],
    BoardUserViewSettingEnum.VIEW_ALL_LEADS.value: [
        BoardPermissionEnum.CAN_VIEW_ASSIGNED_LEADS,
        BoardPermissionEnum.CAN_VIEW_CREATED_LEADS,
        BoardPermissionEnum.CAN_VIEW_OTHER_LEADS,
    ],
}

BOARD_EDIT_SETTING_MAPPING: dict[str, list[BoardPermissionEnum]] = {
    BoardUserEditSettingEnum.ONLY_CREATED_LEADS.value: [BoardPermissionEnum.CAN_EDIT_CREATED_LEADS],
    BoardUserEditSettingEnum.ASSIGNED_AND_CREATED_LEADS.value: [
        BoardPermissionEnum.CAN_EDIT_CREATED_LEADS,
        BoardPermissionEnum.CAN_EDIT_ASSIGNED_LEADS,
    ],
    BoardUserEditSettingEnum.EDIT_ALL_LEADS.value: [
        BoardPermissionEnum.CAN_EDIT_CREATED_LEADS,
        BoardPermissionEnum.CAN_EDIT_ASSIGNED_LEADS,
        BoardPermissionEnum.CAN_EDIT_OTHER_LEADS,
    ],
}

BOARD_DELETE_SETTING_MAPPING: dict[str, list[BoardPermissionEnum]] = {
    BoardUserDeleteSettingEnum.ONLY_CREATED_LEADS.value: [BoardPermissionEnum.CAN_DELETE_CREATED_LEADS],
    BoardUserDeleteSettingEnum.ASSIGNED_AND_CREATED_LEADS.value: [
        BoardPermissionEnum.CAN_DELETE_CREATED_LEADS,
        BoardPermissionEnum.CAN_DELETE_ASSIGNED_LEADS,
    ],
    BoardUserDeleteSettingEnum.DELETE_ALL_LEADS.value: [
        BoardPermissionEnum.CAN_DELETE_CREATED_LEADS,
        BoardPermissionEnum.CAN_DELETE_ASSIGNED_LEADS,
        BoardPermissionEnum.CAN_DELETE_OTHER_LEADS,
    ],
}
